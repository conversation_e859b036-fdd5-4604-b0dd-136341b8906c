const Penalty = require('../models/penalty');
const { encrypt } = require('../utils/encryption');
const MigrationService = require('../services/migrationService');
const MigrationService = require('../services/migrationService');

// Format penalties into the structure expected by the app
const formatPenalties = (items) => {
  const penalties = {
    drinking: [],
    physical: [],
    social: [],
    silly: [],
    creative: []
  };

  items.forEach(item => {
    const { id, text_en, text_es, text_dom, category, isPremium, isDefaultFree, isDefaultPremium } = item;

    if (penalties[category]) {
      penalties[category].push({
        id,
        text_en,
        text_es,
        text_dom,
        isPremium: isPremium || false,
        isDefaultFree: isDefaultFree || false,
        isDefaultPremium: isDefaultPremium || false
      });
    }
  });

  return penalties;
};

// Get all penalties formatted for the game
exports.getAllPenalties = async (req, res) => {
  try {
    console.log('Received request for all penalties (API v1)');

    // Check if migration is enabled (with timeout protection)
    let isMigrationEnabled = false;
    try {
      isMigrationEnabled = await Promise.race([
        MigrationService.isMigrationEnabled(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Migration check timeout')), 2000))
      ]);
      console.log('Migration status:', isMigrationEnabled ? 'ENABLED' : 'DISABLED');
    } catch (error) {
      console.warn('Migration check failed, defaulting to disabled:', error.message);
      isMigrationEnabled = false;
    }

    if (isMigrationEnabled) {
      console.log('Migration enabled - returning migration message instead of real penalties');
      try {
        const migrationPenalties = await Promise.race([
          MigrationService.createMigrationPenaltiesResponse(),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Migration penalties timeout')), 2000))
        ]);

      // Apply encryption if enabled
      if (process.env.ENCRYPTION_KEY) {
        try {
          console.log('Encrypting migration penalty response data with ENCRYPTION_KEY');
          const encryptedData = encrypt(migrationPenalties, process.env.ENCRYPTION_KEY);
          return res.status(200).json({
            encrypted: true,
            data: encryptedData
          });
        } catch (encryptError) {
          console.error('Error encrypting migration penalties:', encryptError);
          // Fall back to unencrypted response
        }
      }

        return res.status(200).json(migrationPenalties);
      } catch (error) {
        console.error('Error getting migration penalties, falling back to normal penalties:', error.message);
        // Fall through to normal penalties
      }
    }

    // Normal operation - find all active penalty items
    const items = await Penalty.find({ active: true });
    console.log(`Found ${items.length} active penalty items`);

    // Format penalties into game structure
    const formattedPenalties = formatPenalties(items);

    // Log penalty stats for debugging
    let stats = {
      total: 0,
      categories: {}
    };

    // Gather stats on penalties
    Object.keys(formattedPenalties).forEach(category => {
      const count = formattedPenalties[category].length;
      stats.categories[category] = count;
      stats.total += count;
    });

    console.log('Penalty stats:', stats);

    // Check if encryption is enabled
    if (process.env.ENCRYPTION_KEY) {
      try {
        console.log('Encrypting penalty response data with ENCRYPTION_KEY');
        const encryptedData = encrypt(formattedPenalties, process.env.ENCRYPTION_KEY);

        // Send the encrypted penalties
        return res.status(200).json({
          encrypted: true,
          data: encryptedData
        });
      } catch (encryptError) {
        console.error('Error encrypting penalties:', encryptError);
        // If encryption fails, fall back to unencrypted response
        console.log('Falling back to unencrypted penalty response');
      }
    } else {
      console.log('ENCRYPTION_KEY not set, sending unencrypted penalty response');
    }

    // Send unencrypted penalties
    res.status(200).json(formattedPenalties);
  } catch (error) {
    console.error('Error fetching penalties:', error);
    res.status(500).json({
      error: 'Failed to fetch penalties',
      message: error.message
    });
  }
};
