# Vercel Deployment Troubleshooting Guide

If you're encountering issues deploying your TapTrap backend to Vercel, follow these troubleshooting steps:

## Common Issues and Solutions

### 1. "Deployment not ready" or "Error" Status

Try the following:

1. **Simplify your deployment:**
   - Start with the API-only deployment (vercel.json has been simplified to just deploy the API)
   - Once the API is working, you can add the admin panel later

2. **Check deployment logs:**
   ```bash
   vercel logs your-deployment-url
   ```

3. **Ensure MongoDB connection works:**
   - Verify your MongoDB Atlas connection string in environment variables
   - Make sure network access allows connections from Vercel (set to allow access from anywhere)

### 2. Environment Variables

Verify that all required environment variables are set in the Vercel dashboard:

1. Go to your project in the Vercel dashboard
2. Navigate to Settings > Environment Variables
3. Add the following variables:
   - `MONGODB_URI` - Your MongoDB Atlas connection string
   - `JWT_SECRET` - Secret for JWT tokens
   - `ADMIN_EMAIL` - Admin login email
   - `ADMIN_PASSWORD` - Admin login password
   - `NODE_ENV` - Set to "production"
   - `CORS_ORIGINS` - Comma-separated list of allowed origins

### 3. Retry Deployment with Debug Flags

Run a deployment with debug flags to get more information:

```bash
vercel --debug
```

### 4. Try Development Deployment First

Deploy to a development URL before going to production:

```bash
vercel
```

Once that's working, deploy to production:

```bash
vercel --prod
```

### 5. Verify Project Structure

Ensure your project structure matches what Vercel expects:

- `src/index.js` - Main entry point
- `vercel.json` - Vercel configuration
- `package.json` - Node.js package configuration

### 6. Check for Missing Dependencies

Make sure all dependencies are properly listed in package.json.

## Step-by-Step Guide for Fresh Deployment

1. **Clear existing deployments:**
   ```bash
   vercel remove taptrap-backend
   ```

2. **Deploy with basic configuration:**
   ```bash
   cd /Users/<USER>/Documents/Projects/TapTrap/backend
   vercel
   ```

3. **Set environment variables in Vercel dashboard**

4. **Deploy to production:**
   ```bash
   vercel --prod
   ```

5. **Verify API is working:**
   Test the API with:
   ```
   curl https://your-vercel-url/api/health
   ```

6. **Update to full configuration:**
   Once the API is working, update vercel.json to include the admin panel.

## Advanced Troubleshooting

If problems persist:

1. **Inspect build output:**
   ```bash
   vercel build --debug
   ```

2. **Check for serverless function limitations:**
   - Vercel serverless functions have size and timeout limitations
   - Consider optimizing your dependencies

3. **Contact Vercel Support:**
   Provide them with your deployment ID and error logs.