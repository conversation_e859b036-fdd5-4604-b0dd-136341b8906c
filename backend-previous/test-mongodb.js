/**
 * <PERSON><PERSON><PERSON> to test MongoDB connection
 * 
 * This script runs the MongoDB connection test from the root directory
 * Run with: node test-mongodb.js
 */

require('dotenv').config();
const { spawn } = require('child_process');

// Verify MONGODB_URI is set
if (!process.env.MONGODB_URI) {
  console.error('ERROR: MONGODB_URI environment variable is not set!');
  console.error('Please make sure you have a .env file with MONGODB_URI defined.');
  process.exit(1);
}

console.log('Running MongoDB connection test...');
console.log('MONGODB_URI is defined:', !!process.env.MONGODB_URI);

// Run the test script
const testProcess = spawn('node', ['src/test-mongodb-connection.js'], {
  stdio: 'inherit',
  env: process.env
});

testProcess.on('close', (code) => {
  if (code !== 0) {
    console.error(`Test process exited with code ${code}`);
    process.exit(code);
  }
});
