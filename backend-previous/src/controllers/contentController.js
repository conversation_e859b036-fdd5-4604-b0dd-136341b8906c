const Content = require('../models/content');
const { encrypt } = require('../utils/encryption');

// Format content into the structure expected by the app
const formatGameContent = (items) => {
  const gameContent = {
    questions: {
      casual_questions: [],
      mild_questions: [],
      spicy_questions: [],
      no_limits_questions: [],
      couple_questions: []
    },
    dares: {
      casual_dares: [],
      mild_dares: [],
      spicy_dares: [],
      no_limits_dares: [],
      couple_dares: []
    }
  };

  items.forEach(item => {
    const { id, text_en, text_es, text_dom, category, gameMode } = item;

    // Skip inactive items
    if (!item.active) return;

    const contentItem = {
      id,
      text_en,
      text_es,
      text_dom
    };

    if (gameMode === 'questions') {
      if (gameContent.questions[category]) {
        gameContent.questions[category].push(contentItem);
      }
    } else if (gameMode === 'dares') {
      // For dares, use the category directly since it should already be in the format 'category_dares'
      if (gameContent.dares[category]) {
        gameContent.dares[category].push(contentItem);
      }
    }
  });

  return gameContent;
};

// Get all content formatted for the game
exports.getAllContent = async (req, res) => {
  try {
    console.log('Received request for all game content');

    // Find all active content items
    const items = await Content.find({ active: true });
    console.log(`Found ${items.length} active content items`);

    // Format content into game structure
    const formattedContent = formatGameContent(items);

    // Log content stats for debugging
    let stats = {
      questions: {
        total: 0,
        categories: {}
      },
      dares: {
        total: 0,
        categories: {}
      }
    };

    // Gather stats on questions
    Object.keys(formattedContent.questions).forEach(category => {
      const count = formattedContent.questions[category].length;
      stats.questions.categories[category] = count;
      stats.questions.total += count;
    });

    // Gather stats on dares
    Object.keys(formattedContent.dares).forEach(category => {
      const count = formattedContent.dares[category].length;
      stats.dares.categories[category] = count;
      stats.dares.total += count;
    });

    console.log('Content stats:', JSON.stringify(stats, null, 2));

    // CORS headers are handled by the global CORS middleware

    // Check if encryption is enabled via environment variable
    console.log('Environment variables check:');
    console.log('- ENCRYPTION_KEY exists:', process.env.ENCRYPTION_KEY ? 'Yes' : 'No');
    console.log('- ENCRYPTION_KEY value length:', process.env.ENCRYPTION_KEY ? process.env.ENCRYPTION_KEY.length : 0);

    if (process.env.ENCRYPTION_KEY) {
      try {
        console.log('Encrypting response data with ENCRYPTION_KEY');
        const encryptedData = encrypt(formattedContent, process.env.ENCRYPTION_KEY);

        // Send the encrypted content
        return res.status(200).json({
          encrypted: true,
          data: encryptedData
        });
      } catch (encryptError) {
        console.error('Error encrypting content:', encryptError);
        // If encryption fails, fall back to unencrypted response
        console.log('Falling back to unencrypted response');
      }
    } else {
      console.log('ENCRYPTION_KEY not set, sending unencrypted response');
      console.log('Available environment variables:', Object.keys(process.env).join(', '));
    }

    // Send the unencrypted formatted content
    res.status(200).json(formattedContent);
  } catch (error) {
    console.error('Error fetching game content:', error);
    res.status(500).json({ message: 'Failed to fetch game content', error: error.message });
  }
};

