const Content = require('../models/content');
const Penalty = require('../models/penalty');
const User = require('../models/user');
const AdSettings = require('../models/adSettings');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const aiContentService = require('../services/aiContentService');

// Admin login
exports.login = async (req, res) => {
  const { email, password } = req.body;

  try {
    console.log('\n------------------------------------');
    console.log('Login attempt received for:', email);

    // Check if JWT_SECRET is defined
    if (!process.env.JWT_SECRET) {
      console.warn('WARNING: JWT_SECRET is not defined in environment! Using fallback secret.');
    }

    // Check for admin login using environment variables
    const adminEmail = process.env.ADMIN_EMAIL;
    const adminPassword = process.env.ADMIN_PASSWORD;

    // Only allow admin login if environment variables are properly set
    if (adminEmail && adminPassword && email === adminEmail && password === adminPassword) {
      console.log('Using admin credentials from environment variables');

      // Create a mock user if it doesn't exist
      let user = await User.findOne({ email }).catch(err => {
        console.log('Error finding user, using mock user:', err.message);
        return null;
      });

      if (!user) {
        console.log('Admin user not found in database, creating one for this session');
        user = {
          _id: '123456789012345678901234', // Mock ObjectId
          email: adminEmail,
          role: 'admin'
        };
      }

      // Generate JWT token using environment variable
      if (!process.env.JWT_SECRET) {
        console.error('JWT_SECRET environment variable is not set!');
        return res.status(500).json({ message: 'Server configuration error' });
      }

      const token = jwt.sign(
        { id: user._id, email: user.email, role: 'admin' },
        process.env.JWT_SECRET,
        { expiresIn: '1d' }
      );

      console.log('Generated token:', token.substring(0, 20) + '...');
      console.log('Login successful');

      return res.status(200).json({
        token,
        user: {
          id: user._id,
          email: user.email,
          role: 'admin'
        }
      });
    }

    // Normal authentication flow for production with registered users
    // Find user by email
    const user = await User.findOne({ email }).catch(err => {
      console.error('Database error finding user:', err);
      return null;
    });

    if (!user) {
      console.log('User not found in database');
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Check password
    let isMatch = false;
    try {
      isMatch = await user.comparePassword(password);
    } catch (err) {
      console.error('Error comparing password:', err);
    }

    if (!isMatch) {
      console.log('Password verification failed');
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    console.log('Login successful - normal authentication flow');

    // Generate JWT token using environment variable
    if (!process.env.JWT_SECRET) {
      console.error('JWT_SECRET environment variable is not set!');
      return res.status(500).json({ message: 'Server configuration error' });
    }

    const token = jwt.sign(
      { id: user._id, email: user.email, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '1d' }
    );

    console.log('Sending response with token:', token.substring(0, 20) + '...');

    res.status(200).json({
      token,
      user: {
        id: user._id,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      message: 'Server error during login',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Create a new content item
exports.createContent = async (req, res) => {
  try {
    console.log('Creating new content with data:', req.body);
    const { text_en, text_es, text_dom, category, gameMode, active = true } = req.body;

    // Validation
    if (!text_en || !text_es || !text_dom || !category || !gameMode) {
      console.log('Validation failed - missing required fields');
      return res.status(400).json({ message: 'All fields are required' });
    }

    // Generate a unique ID for the new content
    const contentId = uuidv4();
    console.log('Generated new content ID:', contentId);

    // Create content object
    const newContent = new Content({
      id: contentId,
      text_en,
      text_es,
      text_dom,
      category,
      gameMode,
      active: active === false ? false : true,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    console.log('Saving content to database...');
    const savedContent = await newContent.save();
    console.log('Content saved successfully:', savedContent);

    res.status(201).json(savedContent);
  } catch (error) {
    console.error('Error creating content:', error);
    res.status(500).json({ message: 'Failed to create content', error: error.message });
  }
};

// Get all content items for admin (including inactive)
exports.getAllContent = async (req, res) => {
  try {
    console.log('Fetching all content items for admin dashboard with query params:', req.query);

    // Build filter object from query parameters
    const filter = {};
    const { gameMode, category, categoryPattern, search, active } = req.query;

    // Apply filters if provided
    if (gameMode) {
      filter.gameMode = gameMode;
    }

    if (category) {
      filter.category = category;
    } else if (categoryPattern) {
      // If categoryPattern is provided (e.g., 'casual_'), use regex to match categories
      filter.category = { $regex: `^${categoryPattern}` };
    }

    if (active !== undefined) {
      filter.active = active === 'true';
    }

    // Handle search term for text_en, text_es, and text_dom
    if (search) {
      filter.$or = [
        { text_en: { $regex: search, $options: 'i' } },
        { text_es: { $regex: search, $options: 'i' } },
        { text_dom: { $regex: search, $options: 'i' } }
      ];
    }

    console.log('Applying filter:', filter);

    // Get content with filters
    const items = await Content.find(filter).sort({ createdAt: -1 });
    console.log(`Found ${items.length} content items matching filters`);

    // CORS headers are handled by the global CORS middleware

    // Pagination (if needed in the future)
    // For now, return all items
    res.status(200).json({
      items,
      totalItems: items.length,
      totalPages: 1,
      currentPage: 1
    });
  } catch (error) {
    console.error('Error fetching all content:', error);
    res.status(500).json({ message: 'Failed to fetch content', error: error.message });
  }
};

// Get content by ID
exports.getContentById = async (req, res) => {
  try {
    const content = await Content.findOne({ id: req.params.id });
    if (!content) {
      return res.status(404).json({ message: 'Content not found' });
    }
    res.status(200).json(content);
  } catch (error) {
    console.error('Error fetching content by ID:', error);
    res.status(500).json({ message: 'Failed to fetch content' });
  }
};

// Update content
exports.updateContent = async (req, res) => {
  try {
    const { text_en, text_es, text_dom, category, gameMode, active } = req.body;

    // Find and update
    const updatedContent = await Content.findOneAndUpdate(
      { id: req.params.id },
      {
        text_en,
        text_es,
        text_dom,
        category,
        gameMode,
        active,
        updatedAt: Date.now()
      },
      { new: true }
    );

    if (!updatedContent) {
      return res.status(404).json({ message: 'Content not found' });
    }

    res.status(200).json(updatedContent);
  } catch (error) {
    console.error('Error updating content:', error);
    res.status(500).json({ message: 'Failed to update content' });
  }
};

// Delete content
exports.deleteContent = async (req, res) => {
  try {
    const deletedContent = await Content.findOneAndDelete({ id: req.params.id });

    if (!deletedContent) {
      return res.status(404).json({ message: 'Content not found' });
    }

    res.status(200).json({ message: 'Content deleted successfully' });
  } catch (error) {
    console.error('Error deleting content:', error);
    res.status(500).json({ message: 'Failed to delete content' });
  }
};

// ===== PENALTY MANAGEMENT =====

// Create a new penalty item
exports.createPenalty = async (req, res) => {
  try {
    console.log('Creating new penalty with data:', req.body);
    const {
      text_en,
      text_es,
      text_dom,
      category,
      active = true,
      isPremium = false,
      isDefaultFree = false,
      isDefaultPremium = false
    } = req.body;

    // Validation
    if (!text_en || !text_es || !text_dom || !category) {
      console.log('Validation failed - missing required fields');
      return res.status(400).json({ message: 'All fields are required' });
    }

    // Ensure only one penalty can be default for each user type
    if (isDefaultFree) {
      await Penalty.updateMany({}, { isDefaultFree: false });
      console.log('Cleared existing default free penalty');
    }

    if (isDefaultPremium) {
      await Penalty.updateMany({}, { isDefaultPremium: false });
      console.log('Cleared existing default premium penalty');
    }

    // Generate a unique ID for the new penalty
    const penaltyId = uuidv4();
    console.log('Generated new penalty ID:', penaltyId);

    // Create penalty object
    const newPenalty = new Penalty({
      id: penaltyId,
      text_en,
      text_es,
      text_dom,
      category,
      active: active === false ? false : true,
      isPremium: isPremium === true ? true : false,
      isDefaultFree: isDefaultFree === true ? true : false,
      isDefaultPremium: isDefaultPremium === true ? true : false,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    console.log('Saving penalty to database...');
    const savedPenalty = await newPenalty.save();
    console.log('Penalty saved successfully:', savedPenalty);

    res.status(201).json(savedPenalty);
  } catch (error) {
    console.error('Error creating penalty:', error);
    res.status(500).json({ message: 'Failed to create penalty', error: error.message });
  }
};

// Get all penalty items for admin (including inactive)
exports.getAllPenalties = async (req, res) => {
  try {
    console.log('Fetching all penalty items for admin dashboard with query params:', req.query);

    // Build filter object from query parameters
    const filter = {};
    const { category, search, active } = req.query;

    // Apply filters if provided
    if (category) {
      filter.category = category;
    }

    if (active !== undefined) {
      filter.active = active === 'true';
    }

    // Handle search term for text_en, text_es, and text_dom
    if (search) {
      filter.$or = [
        { text_en: { $regex: search, $options: 'i' } },
        { text_es: { $regex: search, $options: 'i' } },
        { text_dom: { $regex: search, $options: 'i' } }
      ];
    }

    console.log('Applying penalty filter:', filter);

    // Get penalties with filters
    const items = await Penalty.find(filter).sort({ createdAt: -1 });
    console.log(`Found ${items.length} penalty items matching filters`);

    res.status(200).json({
      items,
      totalItems: items.length,
      totalPages: 1,
      currentPage: 1
    });
  } catch (error) {
    console.error('Error fetching all penalties:', error);
    res.status(500).json({ message: 'Failed to fetch penalties', error: error.message });
  }
};

// Get penalty by ID
exports.getPenaltyById = async (req, res) => {
  try {
    const penalty = await Penalty.findOne({ id: req.params.id });
    if (!penalty) {
      return res.status(404).json({ message: 'Penalty not found' });
    }
    res.status(200).json(penalty);
  } catch (error) {
    console.error('Error fetching penalty by ID:', error);
    res.status(500).json({ message: 'Failed to fetch penalty' });
  }
};

// Update penalty
exports.updatePenalty = async (req, res) => {
  try {
    const {
      text_en,
      text_es,
      text_dom,
      category,
      active,
      isPremium = false,
      isDefaultFree = false,
      isDefaultPremium = false
    } = req.body;

    // Ensure only one penalty can be default for each user type
    if (isDefaultFree) {
      await Penalty.updateMany({ id: { $ne: req.params.id } }, { isDefaultFree: false });
      console.log('Cleared existing default free penalty for other penalties');
    }

    if (isDefaultPremium) {
      await Penalty.updateMany({ id: { $ne: req.params.id } }, { isDefaultPremium: false });
      console.log('Cleared existing default premium penalty for other penalties');
    }

    // Find and update
    const updatedPenalty = await Penalty.findOneAndUpdate(
      { id: req.params.id },
      {
        text_en,
        text_es,
        text_dom,
        category,
        active,
        isPremium: isPremium === true ? true : false,
        isDefaultFree: isDefaultFree === true ? true : false,
        isDefaultPremium: isDefaultPremium === true ? true : false,
        updatedAt: Date.now()
      },
      { new: true }
    );

    if (!updatedPenalty) {
      return res.status(404).json({ message: 'Penalty not found' });
    }

    res.status(200).json(updatedPenalty);
  } catch (error) {
    console.error('Error updating penalty:', error);
    res.status(500).json({ message: 'Failed to update penalty' });
  }
};

// Delete penalty
exports.deletePenalty = async (req, res) => {
  try {
    const deletedPenalty = await Penalty.findOneAndDelete({ id: req.params.id });

    if (!deletedPenalty) {
      return res.status(404).json({ message: 'Penalty not found' });
    }

    res.status(200).json({ message: 'Penalty deleted successfully' });
  } catch (error) {
    console.error('Error deleting penalty:', error);
    res.status(500).json({ message: 'Failed to delete penalty' });
  }
};

// ===== ANALYTICS =====

// Get analytics data for dashboard charts
exports.getAnalytics = async (req, res) => {
  try {
    console.log('Fetching analytics data with query params:', req.query);
    const { timePeriod = 'all' } = req.query;

    // Calculate date range based on time period
    let dateFilter = {};
    const now = new Date();

    switch (timePeriod) {
      case 'thisWeek':
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        startOfWeek.setHours(0, 0, 0, 0);
        dateFilter = { createdAt: { $gte: startOfWeek } };
        break;

      case 'lastWeek':
        const startOfLastWeek = new Date(now);
        startOfLastWeek.setDate(now.getDate() - now.getDay() - 7);
        startOfLastWeek.setHours(0, 0, 0, 0);
        const endOfLastWeek = new Date(startOfLastWeek);
        endOfLastWeek.setDate(startOfLastWeek.getDate() + 6);
        endOfLastWeek.setHours(23, 59, 59, 999);
        dateFilter = {
          createdAt: {
            $gte: startOfLastWeek,
            $lte: endOfLastWeek
          }
        };
        break;

      case 'thisMonth':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        dateFilter = { createdAt: { $gte: startOfMonth } };
        break;

      case 'lastMonth':
        const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
        endOfLastMonth.setHours(23, 59, 59, 999);
        dateFilter = {
          createdAt: {
            $gte: startOfLastMonth,
            $lte: endOfLastMonth
          }
        };
        break;

      case 'last3Months':
        const threeMonthsAgo = new Date(now);
        threeMonthsAgo.setMonth(now.getMonth() - 3);
        dateFilter = { createdAt: { $gte: threeMonthsAgo } };
        break;

      case 'all':
      default:
        // No date filter for all time
        break;
    }

    console.log('Applying date filter:', dateFilter);

    // Aggregate data by category and game mode
    const pipeline = [
      { $match: { active: true, ...dateFilter } },
      {
        $group: {
          _id: {
            category: '$category',
            gameMode: '$gameMode'
          },
          count: { $sum: 1 },
          createdDates: { $push: '$createdAt' }
        }
      },
      {
        $group: {
          _id: '$_id.category',
          questions: {
            $sum: {
              $cond: [{ $eq: ['$_id.gameMode', 'questions'] }, '$count', 0]
            }
          },
          dares: {
            $sum: {
              $cond: [{ $eq: ['$_id.gameMode', 'dares'] }, '$count', 0]
            }
          },
          total: { $sum: '$count' }
        }
      },
      { $sort: { _id: 1 } }
    ];

    const analyticsData = await Content.aggregate(pipeline);
    console.log('Analytics aggregation result:', analyticsData);

    // Transform data for chart consumption
    const categoryMap = {};

    analyticsData.forEach(item => {
      // Extract category name without the game mode suffix
      const categoryName = item._id.replace(/_questions|_dares/, '');
      const formattedCategory = categoryName.charAt(0).toUpperCase() + categoryName.slice(1);

      if (!categoryMap[formattedCategory]) {
        categoryMap[formattedCategory] = {
          category: formattedCategory,
          questions: 0,
          dares: 0,
          total: 0
        };
      }

      categoryMap[formattedCategory].questions += item.questions;
      categoryMap[formattedCategory].dares += item.dares;
      categoryMap[formattedCategory].total += item.total;
    });

    const chartData = Object.values(categoryMap).sort((a, b) => a.category.localeCompare(b.category));

    // Get total counts for summary
    const totalQuestions = chartData.reduce((sum, item) => sum + item.questions, 0);
    const totalDares = chartData.reduce((sum, item) => sum + item.dares, 0);
    const totalContent = totalQuestions + totalDares;

    const response = {
      timePeriod,
      chartData,
      summary: {
        totalContent,
        totalQuestions,
        totalDares
      }
    };

    console.log('Sending analytics response:', response);
    res.status(200).json(response);

  } catch (error) {
    console.error('Error fetching analytics data:', error);
    res.status(500).json({
      message: 'Failed to fetch analytics data',
      error: error.message
    });
  }
};

// ===== AI CONTENT GENERATION =====

// Generate AI content
exports.generateAIContent = async (req, res) => {
  try {
    console.log('AI content generation request received:', req.body);
    const { gameMode, category } = req.body;

    // Validation
    if (!gameMode || !category) {
      return res.status(400).json({
        message: 'Game mode and category are required',
        details: 'Please provide both gameMode (questions/dares) and category'
      });
    }

    // Validate gameMode
    if (!['questions', 'dares'].includes(gameMode)) {
      return res.status(400).json({
        message: 'Invalid game mode',
        details: 'Game mode must be either "questions" or "dares"'
      });
    }

    // Validate category
    const validCategories = [
      'casual_questions', 'mild_questions', 'spicy_questions', 'no_limits_questions', 'couple_questions',
      'casual_dares', 'mild_dares', 'spicy_dares', 'no_limits_dares', 'couple_dares'
    ];

    if (!validCategories.includes(category)) {
      return res.status(400).json({
        message: 'Invalid category',
        details: `Category must be one of: ${validCategories.join(', ')}`
      });
    }

    // Check if AI service is available
    if (!aiContentService.isAvailable()) {
      return res.status(503).json({
        message: 'AI content generation service is not available',
        details: 'OpenAI API key is not configured. Please contact the administrator.'
      });
    }

    // Generate content using AI service
    const generatedContent = await aiContentService.generateContent(gameMode, category);

    console.log(`Successfully generated ${generatedContent.length} AI content items`);

    res.status(200).json({
      success: true,
      gameMode,
      category,
      generatedContent,
      count: generatedContent.length
    });

  } catch (error) {
    console.error('Error generating AI content:', error);

    // Handle specific OpenAI errors
    if (error.message.includes('API key')) {
      return res.status(503).json({
        message: 'AI service configuration error',
        details: 'OpenAI API key is invalid or missing'
      });
    }

    if (error.message.includes('quota')) {
      return res.status(503).json({
        message: 'AI service quota exceeded',
        details: 'OpenAI API quota has been exceeded. Please try again later.'
      });
    }

    res.status(500).json({
      message: 'Failed to generate AI content',
      details: error.message
    });
  }
};

// Bulk create AI generated content
exports.bulkCreateAIContent = async (req, res) => {
  try {
    console.log('Bulk create AI content request received:', req.body);
    const { contentItems, gameMode, category } = req.body;

    // Validation
    if (!contentItems || !Array.isArray(contentItems) || contentItems.length === 0) {
      return res.status(400).json({
        message: 'Content items array is required and cannot be empty'
      });
    }

    if (!gameMode || !category) {
      return res.status(400).json({
        message: 'Game mode and category are required'
      });
    }

    // Validate each content item
    for (let i = 0; i < contentItems.length; i++) {
      const item = contentItems[i];
      if (!item.text_en || !item.text_es || !item.text_dom) {
        return res.status(400).json({
          message: `Content item ${i + 1} is missing required text fields`,
          details: 'Each item must have text_en, text_es, and text_dom'
        });
      }
    }

    // Use AI service to bulk create content
    const createdItems = await aiContentService.bulkCreateContent(contentItems, gameMode, category);

    console.log(`Successfully created ${createdItems.length} content items via bulk create`);

    res.status(201).json({
      success: true,
      message: `Successfully created ${createdItems.length} content items`,
      createdItems,
      count: createdItems.length
    });

  } catch (error) {
    console.error('Error bulk creating AI content:', error);
    res.status(500).json({
      message: 'Failed to bulk create content',
      details: error.message
    });
  }
};

// ===== AD SETTINGS MANAGEMENT =====

// Get ad settings
exports.getAdSettings = async (req, res) => {
  try {
    console.log('Fetching ad settings');

    // Try to find existing settings
    let settings = await AdSettings.findOne({ settingsId: 'default' });

    // If no settings exist, create default settings
    if (!settings) {
      console.log('No ad settings found, creating default settings');
      settings = new AdSettings({
        settingsId: 'default',
        watchAdsButtonEnabled: true, // Legacy field for backward compatibility
        watchAdsButtonEnabledIOS: true,
        watchAdsButtonEnabledAndroid: true,
        requiredAdsCount: 1,
        freeContentLimit: 6
      });
      await settings.save();
      console.log('Default ad settings created');
    }

    console.log('Ad settings retrieved:', settings);
    res.status(200).json(settings);

  } catch (error) {
    console.error('Error fetching ad settings:', error);
    res.status(500).json({
      message: 'Failed to fetch ad settings',
      error: error.message
    });
  }
};

// Update ad settings
exports.updateAdSettings = async (req, res) => {
  try {
    console.log('Updating ad settings with data:', req.body);
    const {
      watchAdsButtonEnabled,
      watchAdsButtonEnabledIOS,
      watchAdsButtonEnabledAndroid,
      requiredAdsCount,
      freeContentLimit
    } = req.body;

    // Prepare update object
    const updateData = {
      requiredAdsCount,
      freeContentLimit,
      updatedAt: new Date()
    };

    // Handle OS-specific settings (new format)
    if (watchAdsButtonEnabledIOS !== undefined && watchAdsButtonEnabledAndroid !== undefined) {
      // New format with OS-specific toggles
      if (typeof watchAdsButtonEnabledIOS !== 'boolean') {
        return res.status(400).json({
          message: 'watchAdsButtonEnabledIOS must be a boolean value'
        });
      }
      if (typeof watchAdsButtonEnabledAndroid !== 'boolean') {
        return res.status(400).json({
          message: 'watchAdsButtonEnabledAndroid must be a boolean value'
        });
      }

      updateData.watchAdsButtonEnabledIOS = watchAdsButtonEnabledIOS;
      updateData.watchAdsButtonEnabledAndroid = watchAdsButtonEnabledAndroid;
      // Also update legacy field for backward compatibility (use iOS value as default)
      updateData.watchAdsButtonEnabled = watchAdsButtonEnabledIOS;

    } else if (watchAdsButtonEnabled !== undefined) {
      // Legacy format - apply to both platforms for backward compatibility
      if (typeof watchAdsButtonEnabled !== 'boolean') {
        return res.status(400).json({
          message: 'watchAdsButtonEnabled must be a boolean value'
        });
      }

      updateData.watchAdsButtonEnabled = watchAdsButtonEnabled;
      updateData.watchAdsButtonEnabledIOS = watchAdsButtonEnabled;
      updateData.watchAdsButtonEnabledAndroid = watchAdsButtonEnabled;
    }

    // Validation for other fields
    if (!Number.isInteger(requiredAdsCount) || requiredAdsCount < 1 || requiredAdsCount > 10) {
      return res.status(400).json({
        message: 'requiredAdsCount must be an integer between 1 and 10'
      });
    }

    if (!Number.isInteger(freeContentLimit) || freeContentLimit < 1 || freeContentLimit > 20) {
      return res.status(400).json({
        message: 'freeContentLimit must be an integer between 1 and 20'
      });
    }

    // Find and update settings, or create if doesn't exist
    let settings = await AdSettings.findOneAndUpdate(
      { settingsId: 'default' },
      updateData,
      {
        new: true,
        upsert: true // Create if doesn't exist
      }
    );

    console.log('Ad settings updated successfully:', settings);
    res.status(200).json(settings);

  } catch (error) {
    console.error('Error updating ad settings:', error);
    res.status(500).json({
      message: 'Failed to update ad settings',
      error: error.message
    });
  }
};