const mongoose = require('mongoose');

const excludedContentSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  text_en: {
    type: String,
    required: true
  },
  text_es: {
    type: String,
    required: true
  },
  text_dom: {
    type: String,
    required: true
  },
  gameMode: {
    type: String,
    required: true,
    enum: ['questions', 'dares']
  },
  category: {
    type: String,
    required: true
  },
  excludedAt: {
    type: Date,
    default: Date.now
  },
  reason: {
    type: String,
    default: 'Manual exclusion by admin'
  },
  originalContentId: {
    type: String,
    required: false // ID del contenido original si existe
  }
}, {
  timestamps: true,
  collection: 'excludedContent'
});

// Índices para mejorar performance
excludedContentSchema.index({ gameMode: 1, category: 1 });
excludedContentSchema.index({ excludedAt: -1 });
excludedContentSchema.index({ originalContentId: 1 });

module.exports = mongoose.model('ExcludedContent', excludedContentSchema);
