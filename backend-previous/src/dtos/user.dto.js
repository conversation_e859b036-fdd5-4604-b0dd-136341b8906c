/**
 * User DTO
 * Data Transfer Object for User entity
 */

const BaseDTO = require('./base.dto');

class UserDTO extends BaseDTO {
  /**
   * Validate user data
   * @param {Object} data - User data to validate
   * @returns {Object} - Validation result with isValid and errors
   */
  static validate(data) {
    // Check required fields
    const requiredFields = ['email', 'password'];
    const requiredValidation = this.validateRequired(data, requiredFields);
    
    if (!requiredValidation.isValid) {
      return requiredValidation;
    }
    
    // Additional validations
    const errors = {};
    let isValid = true;
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      errors.email = 'Invalid email format';
      isValid = false;
    }
    
    // Validate password length
    if (data.password.length < 6) {
      errors.password = 'Password must be at least 6 characters long';
      isValid = false;
    }
    
    // Validate role if provided
    if (data.role && data.role !== 'admin') {
      errors.role = 'Role must be "admin"';
      isValid = false;
    }
    
    return { isValid, errors };
  }
  
  /**
   * Transform user data from API to entity format
   * @param {Object} data - User data from API
   * @returns {Object} - User entity
   */
  static toEntity(data) {
    return {
      email: data.email.trim().toLowerCase(),
      password: data.password,
      role: data.role || 'admin',
      // createdAt and updatedAt are handled by the model
    };
  }
  
  /**
   * Transform user entity to API format (without sensitive data)
   * @param {Object} entity - User entity
   * @returns {Object} - User data for API
   */
  static fromEntity(entity) {
    // If entity is a Mongoose document, convert to plain object
    const plainEntity = entity.toObject ? entity.toObject() : entity;
    
    // Remove sensitive data
    const { password, ...userWithoutPassword } = plainEntity;
    
    return userWithoutPassword;
  }
}

module.exports = UserDTO;
