const WhatsNew = require('../models/whatsNew');

/**
 * Seed initial What's New logs for development and testing
 */
const seedWhatsNewLogs = async () => {
  try {
    console.log('Seeding What\'s New logs...');

    // Check if logs already exist
    const existingLogs = await WhatsNew.countDocuments();
    if (existingLogs > 0) {
      console.log(`What's New logs already exist (${existingLogs} logs found). Skipping seed.`);
      return;
    }

    const sampleLogs = [
      {
        type: 'purple',
        date: 'DEC 15, 2024',
        title_en: 'PENALTIES SYSTEM',
        title_es: 'SISTEMA DE CASTIGOS',
        title_dom: 'SISTEMA DE CASTIGOS',
        description_en: 'Now you can set penalties for not completing the dares. Choose from drinking, physical, social, silly, and creative penalties.',
        description_es: 'Ahora puedes establecer castigos por no completar los retos. Elige entre castigos de bebida, físicos, sociales, divertidos y creativos.',
        description_dom: 'Ahora puedes poner castigos por no hacer los retos. Elige entre castigos de bebida, físicos, sociales, divertidos y creativos.',
        videoUrl: 'https://www.youtube.com/watch?v=example1',
        appVersion: '1.0.10',
        platform: 'both'
      },
      {
        type: 'cyan',
        date: 'NOV 28, 2024',
        title_en: 'PUSH NOTIFICATIONS',
        title_es: 'NOTIFICACIONES PUSH',
        title_dom: 'NOTIFICACIONES PUSH',
        description_en: 'Get notified about new content and features. Stay up to date with the latest TapTrap updates.',
        description_es: 'Recibe notificaciones sobre nuevo contenido y funciones. Mantente al día con las últimas actualizaciones de TapTrap.',
        description_dom: 'Recibe notificaciones sobre contenido nuevo y funciones. Mantente al día con las últimas actualizaciones de TapTrap.',
        appVersion: '1.0.8',
        platform: 'both'
      },
      {
        type: 'red',
        date: 'NOV 10, 2024',
        title_en: 'AD-BASED PREMIUM',
        title_es: 'PREMIUM CON ANUNCIOS',
        title_dom: 'PREMIUM CON ANUNCIOS',
        description_en: 'Watch ads to unlock premium content temporarily. Enjoy premium features without a subscription.',
        description_es: 'Ve anuncios para desbloquear contenido premium temporalmente. Disfruta de funciones premium sin suscripción.',
        description_dom: 'Ve anuncios pa\' desbloquear contenido premium temporalmente. Disfruta funciones premium sin suscripción.',
        videoUrl: 'https://www.youtube.com/watch?v=example2',
        appVersion: '1.0.5',
        platform: 'both'
      },
      {
        type: 'dark',
        date: 'OCT 25, 2024',
        title_en: 'NEW CATEGORIES',
        title_es: 'NUEVAS CATEGORÍAS',
        title_dom: 'NUEVAS CATEGORÍAS',
        description_en: 'We added new question and dare categories including "No Limits" for the most adventurous players.',
        description_es: 'Agregamos nuevas categorías de preguntas y retos incluyendo "Sin Límites" para los jugadores más aventureros.',
        description_dom: 'Agregamos nuevas categorías de preguntas y retos incluyendo "Sin Límites" pa\' los jugadores más aventureros.',
        appVersion: '1.0.0',
        platform: 'both'
      }
    ];

    // Insert sample logs
    const createdLogs = await WhatsNew.insertMany(sampleLogs);
    console.log(`✅ Successfully seeded ${createdLogs.length} What's New logs`);

    // Log the created logs for verification
    createdLogs.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.title} (${log.type}) - v${log.appVersion}`);
    });

  } catch (error) {
    console.error('❌ Error seeding What\'s New logs:', error);
    throw error;
  }
};

module.exports = seedWhatsNewLogs;
