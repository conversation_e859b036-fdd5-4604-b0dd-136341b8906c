require('dotenv').config();
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const Content = require('../models/content');
const { v4: uuidv4 } = require('uuid');

/**
 * This script imports the game content from a JSON file
 * exported from Airtable into the MongoDB database
 */
const importAirtableData = async () => {
  try {
    // Connect to the database with optimized connection options
    const mongooseOptions = {
      serverSelectionTimeoutMS: 15000, // Timeout for server selection
      socketTimeoutMS: 30000,         // Timeout for socket operations
      connectTimeoutMS: 30000,        // Timeout for initial connection
    };
    await mongoose.connect(process.env.MONGODB_URI, mongooseOptions);
    console.log('Connected to MongoDB');

    // Log the collection name being used
    console.log('Using collection:', Content.collection.name);
    
    // Path to the exported JSON file (you should export your Airtable data first)
    const filePath = path.resolve(__dirname, '../../data/airtableExport.json');
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error('File not found. Please export your Airtable data first.');
      process.exit(1);
    }
    
    // Read and parse the JSON file
    const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    // Track import statistics
    let importCount = 0;
    let errorCount = 0;
    
    // Process each record
    for (const record of jsonData) {
      try {
        const { fields } = record;
        const { text_en, text_es, category, gameMode, id: fieldId } = fields;
        
        if (text_en && category && gameMode) {
          // Check if content already exists
          const existingContent = await Content.findOne({ id: fieldId });
          
          if (existingContent) {
            console.log(`Content with ID ${fieldId} already exists. Skipping.`);
            continue;
          }
          
          // Create new content item
          const contentItem = new Content({
            id: fieldId || uuidv4(),
            text_en,
            text_es: text_es || text_en, // Default to English if Spanish is missing
            category,
            gameMode,
            active: true
          });
          
          await contentItem.save();
          importCount++;
          console.log(`Imported content: ${text_en.substring(0, 30)}...`);
        } else {
          console.warn('Incomplete record:', record);
          errorCount++;
        }
      } catch (error) {
        console.error('Error importing record:', error);
        errorCount++;
      }
    }
    
    console.log(`Import completed. Successfully imported ${importCount} items. Errors: ${errorCount}`);
    
    // Disconnect from database
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
    process.exit(0);
  } catch (error) {
    console.error('Error importing data:', error);
    process.exit(1);
  }
};

// Run the import
importAirtableData();