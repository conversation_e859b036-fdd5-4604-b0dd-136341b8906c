const axios = require('axios');
const OpenAI = require('openai');
const Content = require('../models/content');
const PromptManager = require('../prompts/PromptManager');
const ExcludedContentService = require('./excludedContentService');

class AIContentService {
  constructor() {
    // Grok API configuration
    this.grokApiKey = process.env.GROK_API_KEY || 'xai-lQcihNa5VJlXgbfyCrseDvGgFslVrt3eL8V2WDUp5RQgAtGtn5Jm4sbTZVjaqZHLVaAPti4QnITEMyzL';
    this.grokBaseUrl = 'https://api.x.ai/v1';

    // OpenAI configuration
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.openai = null;

    // Initialize OpenAI client if API key is available
    if (this.openaiApiKey) {
      this.openai = new OpenAI({
        apiKey: this.openaiApiKey
      });
      console.log('OpenAI service initialized successfully');
    } else {
      console.warn('OpenAI API key not found. OpenAI content generation will not be available.');
    }

    // Check overall availability
    if (!this.grokApiKey && !this.openaiApiKey) {
      console.warn('No AI API keys found. AI content generation will not be available.');
      this.available = false;
    } else {
      this.available = true;
      console.log('AI Content Service initialized successfully');
    }
  }

  /**
   * Check if AI service is available
   * @returns {boolean}
   */
  isAvailable() {
    return this.available;
  }

  /**
   * Determine which AI provider to use based on category
   * @param {string} category - Category name
   * @returns {string} - 'openai' or 'grok'
   */
  getProviderForCategory(category) {
    // Extract base category name (remove _questions or _dares suffix)
    const baseCategory = category.split('_')[0];

    // Use OpenAI for casual and mild, Grok for spicy and no_limits
    if (baseCategory === 'casual' || baseCategory === 'mild') {
      return this.openai ? 'openai' : 'grok'; // Fallback to Grok if OpenAI not available
    } else {
      return this.grokApiKey ? 'grok' : 'openai'; // Fallback to OpenAI if Grok not available
    }
  }

  /**
   * Get existing content for context
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @returns {Promise<Array>} - Array of existing content
   */
  async getExistingContent(gameMode, category) {
    try {
      const existingContent = await Content.find({
        gameMode,
        category,
        active: true
      }).limit(10); // Get up to 10 examples for context

      return existingContent.map(item => ({
        text_en: item.text_en,
        text_es: item.text_es,
        text_dom: item.text_dom
      }));
    } catch (error) {
      console.error('Error fetching existing content:', error);
      return [];
    }
  }

  /**
   * Build AI prompt using the new modular prompt system
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @param {Array} existingContent - existing content for context
   * @returns {Object} - Generated prompt configuration
   */
  buildPrompt(gameMode, category, existingContent) {
    try {
      // Validate inputs using PromptManager
      const validation = PromptManager.validatePromptConfiguration(gameMode, category);
      if (!validation.isValid) {
        throw new Error(`Invalid prompt configuration: ${validation.errors.join(', ')}`);
      }

      // Log any warnings
      if (validation.warnings.length > 0) {
        console.warn('Prompt configuration warnings:', validation.warnings);
      }

      // Build the complete prompt using PromptManager
      const promptConfig = PromptManager.buildPrompt(gameMode, category, existingContent, {
        count: 8,
        includeExamples: true,
        useExistingContent: true
      });

      console.log(`Built prompt for ${gameMode} - ${category}:`, {
        categoryDescription: promptConfig.metadata.categoryDescription,
        contentType: promptConfig.metadata.contentType,
        hasExistingContent: existingContent.length > 0,
        promptLength: promptConfig.userPrompt.length
      });

      return promptConfig;
    } catch (error) {
      console.error('Error building prompt:', error);
      throw error;
    }
  }

  /**
   * Get excluded content for a specific game mode and category
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @returns {Promise<string>} - Formatted exclusion prompt text
   */
  async getExclusionPrompt(gameMode, category) {
    return await ExcludedContentService.buildExclusionPrompt(gameMode, category);
  }

  /**
   * Build enhanced prompt with excluded content to avoid repetition
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @param {Array} existingContent - existing content
   * @param {string} exclusionPrompt - formatted exclusion prompt text
   * @returns {Object} - Prompt configuration
   */
  buildPromptWithExclusions(gameMode, category, existingContent, exclusionPrompt) {
    // Use base prompt from PromptManager
    const basePrompt = this.buildPrompt(gameMode, category, existingContent);

    // Create thematic variations to force diversity
    const thematicVariations = this.getThematicVariations(category);

    // Enhance system message with exclusion instructions
    const enhancedSystemMessage = `${basePrompt.systemMessage}

${exclusionPrompt}

🎯 CREATIVITY & ORIGINALITY INSTRUCTIONS:
- BE EXTREMELY CREATIVE AND ORIGINAL
- Use COMPLETELY DIFFERENT themes, scenarios, and vocabulary
- Think outside the box and be innovative
- Each question/dare should feel fresh and unique
- Vary your sentence structures and approaches
- Use unexpected angles and perspectives

🎯 THEMATIC DIVERSITY:
Focus on these varied themes (pick different ones for each item):
${thematicVariations.join('\n')}

⚡ CREATIVITY BOOST:
- Use higher creativity and randomness
- Combine unexpected elements
- Create surprising scenarios
- Think like you're writing for the first time
- Avoid predictable patterns`;

    // Enhance user prompt with specific instructions
    const enhancedUserPrompt = `${basePrompt.userPrompt}

IMPORTANT: Generate 8 COMPLETELY UNIQUE and ORIGINAL items that are TOTALLY DIFFERENT from anything that has been created before. Each item should:
- Use FRESH vocabulary and concepts
- Explore DIFFERENT scenarios and situations
- Have UNIQUE angles and perspectives
- Feel SURPRISING and UNEXPECTED
- Avoid repetitive patterns or themes
- NOT be similar to any excluded content mentioned above

Be maximally creative and original!`;

    return {
      ...basePrompt,
      systemMessage: enhancedSystemMessage,
      userPrompt: enhancedUserPrompt,
      generationParams: {
        ...basePrompt.generationParams,
        temperature: 0.9, // Increase creativity
        top_p: 0.95, // More diversity in token selection
      }
    };
  }

  /**
   * Obtiene variaciones temáticas para una categoría
   * @param {string} category - categoría del contenido
   * @returns {Array} - Array de temas variados
   */
  getThematicVariations(category) {
    const baseThemes = {
      casual_questions: [
        "- Childhood memories and embarrassing moments",
        "- Social situations and awkward encounters",
        "- Technology and modern life dilemmas",
        "- Food preferences and weird eating habits",
        "- Travel experiences and cultural differences",
        "- Work/school situations and funny stories",
        "- Family dynamics and generational gaps",
        "- Friendship challenges and loyalty tests",
        "- Personal habits and quirky behaviors",
        "- Dreams, aspirations, and life goals"
      ],
      mild_questions: [
        "- Personal boundaries and comfort zones",
        "- Moral dilemmas and ethical choices",
        "- Relationship dynamics and dating experiences",
        "- Social pressure and peer influence",
        "- Personal growth and self-discovery",
        "- Trust issues and betrayal experiences",
        "- Competition and jealousy situations",
        "- Personal secrets and hidden truths",
        "- Life regrets and missed opportunities",
        "- Personal values and belief systems"
      ],
      spicy_questions: [
        "- Sexual preferences and intimate experiences",
        "- Attraction patterns and physical desires",
        "- Relationship boundaries and sexual exploration",
        "- Fantasy scenarios and role-playing",
        "- Sexual confidence and performance",
        "- Intimate communication and desires",
        "- Sexual experimentation and curiosity",
        "- Physical attraction and chemistry",
        "- Sexual satisfaction and fulfillment",
        "- Intimate relationships and connections"
      ],
      no_limits_questions: [
        "- Extreme sexual fantasies and taboo desires",
        "- Provocative scenarios and boundary-pushing situations",
        "- Raw sexual experiences and wild encounters",
        "- Forbidden attractions and secret desires",
        "- Extreme relationship dynamics and power play",
        "- Controversial sexual topics and experiences",
        "- Wild party experiences and crazy nights",
        "- Extreme physical experiences and sensations",
        "- Provocative social situations and dares",
        "- Boundary-breaking experiences and adventures"
      ]
    };

    return baseThemes[category] || baseThemes.casual_questions;
  }

  /**
   * Generate content using OpenAI
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @param {Object} promptConfig - Prompt configuration
   * @returns {Promise<string>} - Generated content response
   */
  async generateWithOpenAI(gameMode, category, promptConfig) {
    if (!this.openai) {
      throw new Error('OpenAI service is not available. Please check your API key configuration.');
    }

    console.log(`Generating content with OpenAI for ${gameMode} - ${category}`);

    const completion = await this.openai.chat.completions.create({
      model: 'gpt-4o-mini', // Cost-effective model
      messages: [
        {
          role: "system",
          content: promptConfig.systemMessage
        },
        {
          role: "user",
          content: promptConfig.userPrompt
        }
      ],
      max_tokens: promptConfig.generationParams.maxTokens,
      temperature: promptConfig.generationParams.temperature,
    });

    const responseText = completion.choices[0].message.content.trim();
    console.log('OpenAI response received:', responseText.substring(0, 200) + '...');

    return responseText;
  }

  /**
   * Generate content using Grok AI
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @param {Object} promptConfig - Prompt configuration
   * @returns {Promise<string>} - Generated content response
   */
  async generateWithGrok(gameMode, category, promptConfig) {
    if (!this.grokApiKey) {
      throw new Error('Grok AI service is not available. Please check your API key configuration.');
    }

    console.log(`Generating content with Grok for ${gameMode} - ${category}`);

    const response = await axios.post(`${this.grokBaseUrl}/chat/completions`, {
      model: 'grok-2-1212', // Use Grok's latest model
      messages: [
        {
          role: "system",
          content: promptConfig.systemMessage
        },
        {
          role: "user",
          content: promptConfig.userPrompt
        }
      ],
      max_tokens: promptConfig.generationParams.maxTokens,
      temperature: promptConfig.generationParams.temperature,
    }, {
      headers: {
        'Authorization': `Bearer ${this.grokApiKey}`,
        'Content-Type': 'application/json'
      }
    });

    const responseText = response.data.choices[0].message.content.trim();
    console.log('Grok response received:', responseText.substring(0, 200) + '...');

    // Check if Grok refused to generate content
    if (responseText.includes("I'm sorry") || responseText.includes("I can't assist") || responseText.includes("I cannot")) {
      throw new Error('Grok AI refused to generate this content due to content policy. The prompt may be too explicit. Try using a less explicit category or modify the prompt.');
    }

    return responseText;
  }

  /**
   * Generate content using the appropriate AI provider
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @returns {Promise<Array>} - Generated content array
   */
  async generateContent(gameMode, category) {
    if (!this.isAvailable()) {
      throw new Error('AI service is not available. Please check your API key configuration.');
    }

    try {
      console.log(`Generating AI content for ${gameMode} - ${category}`);

      // Determine which provider to use
      const provider = this.getProviderForCategory(category);
      console.log(`Using ${provider.toUpperCase()} for ${category} category`);

      // Get excluded content to avoid generating similar content
      const exclusionPrompt = await this.getExclusionPrompt(gameMode, category);
      console.log(`Retrieved exclusion prompt for ${gameMode} - ${category}:`, exclusionPrompt ? 'Found excluded content' : 'No excluded content');

      // Get existing content for context
      const existingContent = await this.getExistingContent(gameMode, category);

      // Build prompt using the new modular system with exclusion enhancements
      const promptConfig = this.buildPromptWithExclusions(gameMode, category, existingContent, exclusionPrompt);

      // Generate content with the appropriate provider
      let responseText;
      if (provider === 'openai') {
        responseText = await this.generateWithOpenAI(gameMode, category, promptConfig);
      } else {
        responseText = await this.generateWithGrok(gameMode, category, promptConfig);
      }

      // Parse the JSON response
      let generatedContent;
      try {
        // Remove any markdown code blocks if present
        const cleanedResponse = responseText.replace(/```json\n?|\n?```/g, '').trim();
        generatedContent = JSON.parse(cleanedResponse);
      } catch (parseError) {
        console.error('Error parsing Grok response:', parseError);
        throw new Error('Failed to parse AI response. Please try again.');
      }

      // Validate the response structure
      if (!Array.isArray(generatedContent)) {
        throw new Error('AI response is not in the expected array format.');
      }

      // Validate each item has required fields and fits the category
      const validatedContent = generatedContent.filter(item => {
        // Basic field validation
        const hasRequiredFields = item.text_en && item.text_es && item.text_dom &&
               typeof item.text_en === 'string' &&
               typeof item.text_es === 'string' &&
               typeof item.text_dom === 'string';

        if (!hasRequiredFields) {
          console.warn('Item missing required fields:', item);
          return false;
        }

        // Category-specific validation using PromptManager
        // Temporarily disabled strict validation to allow AI-generated content
        // const isValidForCategory = PromptManager.validateContentForCategory(item, category);
        // if (!isValidForCategory) {
        //   console.warn(`Item does not fit category ${category}:`, item.text_en);
        //   return false;
        // }

        return true;
      });

      if (validatedContent.length === 0) {
        throw new Error('No valid content items were generated.');
      }

      console.log(`Successfully generated ${validatedContent.length} content items`);
      return validatedContent;

    } catch (error) {
      console.error('Error generating AI content:', error);

      // Log more details about the error
      if (error.response) {
        console.error('AI API Error Response:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        });
        throw new Error(`AI API Error: ${error.response.status} - ${error.response.statusText}. ${JSON.stringify(error.response.data)}`);
      } else if (error.request) {
        console.error('AI API Request Error:', error.request);
        throw new Error('Failed to connect to AI API. Please check your internet connection.');
      } else {
        throw error;
      }
    }
  }

  /**
   * Bulk create content items
   * @param {Array} contentItems - Array of content items to create
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @returns {Promise<Array>} - Array of created content items
   */
  async bulkCreateContent(contentItems, gameMode, category) {
    try {
      const createdItems = [];

      for (const item of contentItems) {
        // Generate unique ID
        const { v4: uuidv4 } = require('uuid');
        const contentId = uuidv4();

        // Create content object
        const newContent = new Content({
          id: contentId,
          text_en: item.text_en,
          text_es: item.text_es,
          text_dom: item.text_dom,
          category,
          gameMode,
          active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        const savedContent = await newContent.save();
        createdItems.push(savedContent);
      }

      console.log(`Successfully created ${createdItems.length} content items`);
      return createdItems;

    } catch (error) {
      console.error('Error bulk creating content:', error);
      throw error;
    }
  }
}

module.exports = new AIContentService();
