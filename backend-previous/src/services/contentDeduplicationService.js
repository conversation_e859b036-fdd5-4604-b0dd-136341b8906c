const Content = require('../models/content');

class ContentDeduplicationService {
  constructor() {
    this.usedConcepts = new Map(); // Cache de conceptos usados
    this.similarityThreshold = 0.7; // 70% de similitud
  }

  /**
   * Extrae conceptos clave de un texto
   * @param {string} text - Texto a analizar
   * @returns {Array} - Array de conceptos clave
   */
  extractConcepts(text) {
    if (!text) return [];
    
    // Convertir a minúsculas y limpiar
    const cleanText = text.toLowerCase()
      .replace(/[¿?¡!.,;:]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Palabras a ignorar (stop words en español)
    const stopWords = [
      'el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'al', 'del', 'los', 'las', 'una', 'como', 'pero', 'sus', 'le', 'ya', 'o', 'fue', 'este', 'ha', 'si', 'porque', 'esta', 'son', 'entre', 'cuando', 'muy', 'sin', 'sobre', 'ser', 'tiene', 'también', 'me', 'hasta', 'hay', 'donde', 'han', 'quien', 'están', 'estado', 'desde', 'todo', 'nos', 'durante', 'todos', 'uno', 'les', 'ni', 'contra', 'otros', 'fueron', 'ese', 'eso', 'había', 'ante', 'ellos', 'e', 'esto', 'mí', 'antes', 'algunos', 'qué', 'unos', 'yo', 'otro', 'otras', 'otra', 'él', 'tanto', 'esa', 'estos', 'mucho', 'quienes', 'nada', 'muchos', 'cual', 'poco', 'ella', 'estar', 'haber', 'estas', 'estaba', 'estamos', 'algunas', 'algo', 'nosotros', 'mi', 'mis', 'tú', 'te', 'ti', 'tu', 'tus', 'ellas', 'nosotras', 'vosotros', 'vosotras', 'os', 'mío', 'mía', 'míos', 'mías', 'tuyo', 'tuya', 'tuyos', 'tuyas', 'suyo', 'suya', 'suyos', 'suyas', 'nuestro', 'nuestra', 'nuestros', 'nuestras', 'vuestro', 'vuestra', 'vuestros', 'vuestras', 'esos', 'esas', 'estoy', 'estás', 'está', 'estamos', 'estáis', 'están', 'esté', 'estés', 'estemos', 'estéis', 'estén', 'estaré', 'estarás', 'estará', 'estaremos', 'estaréis', 'estarán', 'estaría', 'estarías', 'estaríamos', 'estaríais', 'estarían', 'estuve', 'estuviste', 'estuvo', 'estuvimos', 'estuvisteis', 'estuvieron', 'estuviera', 'estuvieras', 'estuviéramos', 'estuvierais', 'estuvieran', 'estuviese', 'estuvieses', 'estuviésemos', 'estuvieseis', 'estuviesen', 'estando', 'estado', 'estad', 'he', 'has', 'hemos', 'habéis', 'haya', 'hayas', 'hayamos', 'hayáis', 'hayan', 'habré', 'habrás', 'habrá', 'habremos', 'habréis', 'habrán', 'habría', 'habrías', 'habríamos', 'habríais', 'habrían', 'hube', 'hubiste', 'hubo', 'hubimos', 'hubisteis', 'hubieron', 'hubiera', 'hubieras', 'hubiéramos', 'hubierais', 'hubieran', 'hubiese', 'hubieses', 'hubiésemos', 'hubieseis', 'hubiesen', 'habiendo', 'habido', 'haber', 'cuál', 'cuáles', 'cómo', 'dónde', 'cuándo', 'cuánto', 'cuánta', 'cuántos', 'cuántas'
    ];

    // Extraer palabras significativas
    const words = cleanText.split(' ')
      .filter(word => word.length > 2)
      .filter(word => !stopWords.includes(word))
      .filter(word => !/^\d+$/.test(word)); // Excluir números

    // Extraer frases clave (2-3 palabras consecutivas)
    const phrases = [];
    for (let i = 0; i < words.length - 1; i++) {
      if (words[i] && words[i + 1]) {
        phrases.push(`${words[i]} ${words[i + 1]}`);
      }
      if (words[i] && words[i + 1] && words[i + 2]) {
        phrases.push(`${words[i]} ${words[i + 1]} ${words[i + 2]}`);
      }
    }

    return [...new Set([...words, ...phrases])]; // Eliminar duplicados
  }

  /**
   * Calcula la similitud entre dos textos usando Jaccard similarity
   * @param {string} text1 - Primer texto
   * @param {string} text2 - Segundo texto
   * @returns {number} - Similitud entre 0 y 1
   */
  calculateSimilarity(text1, text2) {
    const concepts1 = new Set(this.extractConcepts(text1));
    const concepts2 = new Set(this.extractConcepts(text2));

    const intersection = new Set([...concepts1].filter(x => concepts2.has(x)));
    const union = new Set([...concepts1, ...concepts2]);

    if (union.size === 0) return 0;
    return intersection.size / union.size;
  }

  /**
   * Verifica si el contenido es demasiado similar al existente
   * @param {Object} newContent - Nuevo contenido a verificar
   * @param {string} gameMode - questions o dares
   * @param {string} category - categoría del contenido
   * @returns {Promise<Object>} - {isDuplicate: boolean, similarContent: Object|null, similarity: number}
   */
  async checkForDuplicates(newContent, gameMode, category) {
    try {
      // Buscar contenido existente de la misma categoría y modo de juego
      const existingContent = await Content.find({
        'challenges.gameMode': gameMode,
        'challenges.category': category
      }).limit(200); // Limitar para performance

      let maxSimilarity = 0;
      let mostSimilarContent = null;

      for (const content of existingContent) {
        const challenge = content.challenges.find(c => c.gameMode === gameMode && c.category === category);
        if (!challenge) continue;

        // Comparar con texto en español (principal)
        const similarity = this.calculateSimilarity(newContent.text_es, challenge.text_es);
        
        if (similarity > maxSimilarity) {
          maxSimilarity = similarity;
          mostSimilarContent = challenge;
        }

        // Si encontramos alta similitud, no necesitamos seguir buscando
        if (similarity > this.similarityThreshold) {
          break;
        }
      }

      return {
        isDuplicate: maxSimilarity > this.similarityThreshold,
        similarContent: mostSimilarContent,
        similarity: maxSimilarity
      };

    } catch (error) {
      console.error('Error checking for duplicates:', error);
      return {
        isDuplicate: false,
        similarContent: null,
        similarity: 0
      };
    }
  }

  /**
   * Obtiene conceptos usados recientemente para una categoría
   * @param {string} gameMode - questions o dares
   * @param {string} category - categoría del contenido
   * @param {number} limit - límite de contenido reciente a analizar
   * @returns {Promise<Array>} - Array de conceptos usados
   */
  async getRecentConcepts(gameMode, category, limit = 50) {
    try {
      const recentContent = await Content.find({
        'challenges.gameMode': gameMode,
        'challenges.category': category
      })
      .sort({ createdAt: -1 })
      .limit(limit);

      const allConcepts = new Set();

      for (const content of recentContent) {
        const challenge = content.challenges.find(c => c.gameMode === gameMode && c.category === category);
        if (challenge) {
          const concepts = this.extractConcepts(challenge.text_es);
          concepts.forEach(concept => allConcepts.add(concept));
        }
      }

      return Array.from(allConcepts);

    } catch (error) {
      console.error('Error getting recent concepts:', error);
      return [];
    }
  }

  /**
   * Filtra contenido duplicado de un array de contenido generado
   * @param {Array} generatedContent - Array de contenido generado
   * @param {string} gameMode - questions o dares
   * @param {string} category - categoría del contenido
   * @returns {Promise<Array>} - Array de contenido filtrado
   */
  async filterDuplicates(generatedContent, gameMode, category) {
    const filteredContent = [];
    const processedConcepts = new Set();

    for (const content of generatedContent) {
      // Verificar duplicados con contenido existente
      const duplicateCheck = await this.checkForDuplicates(content, gameMode, category);
      
      if (duplicateCheck.isDuplicate) {
        console.log(`Contenido duplicado detectado (similitud: ${(duplicateCheck.similarity * 100).toFixed(1)}%):`, content.text_es);
        continue;
      }

      // Verificar duplicados dentro del mismo lote generado
      const contentConcepts = this.extractConcepts(content.text_es);
      const conceptKey = contentConcepts.slice(0, 3).join('_'); // Usar primeros 3 conceptos como clave

      if (processedConcepts.has(conceptKey)) {
        console.log('Contenido similar en el mismo lote:', content.text_es);
        continue;
      }

      processedConcepts.add(conceptKey);
      filteredContent.push(content);
    }

    return filteredContent;
  }
}

module.exports = new ContentDeduplicationService();
