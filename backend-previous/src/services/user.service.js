/**
 * User Service
 * Handles operations related to user entities
 */

const User = require('../models/user');
const UserDTO = require('../dtos/user.dto');
const jwt = require('jsonwebtoken');

class UserService {
  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} - Created user
   */
  async createUser(userData) {
    try {
      // Validate user data
      const validation = UserDTO.validate(userData);
      if (!validation.isValid) {
        throw new Error(`Invalid user data: ${JSON.stringify(validation.errors)}`);
      }

      // Check if user already exists
      const existingUser = await User.findOne({ email: userData.email });
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Transform to entity format
      const userEntity = UserDTO.toEntity(userData);

      // Create and save new user
      const newUser = new User(userEntity);
      await newUser.save();

      return UserDTO.fromEntity(newUser);
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Authenticate user and generate JWT token
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} - Authentication result with token
   */
  async authenticateUser(email, password) {
    try {
      // Find user by email
      const user = await User.findOne({ email });
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        throw new Error('Invalid email or password');
      }

      // Generate JWT token
      const token = jwt.sign(
        { userId: user._id, email: user.email, role: user.role },
        process.env.JWT_SECRET,
        { expiresIn: '24h' }
      );

      return {
        user: UserDTO.fromEntity(user),
        token
      };
    } catch (error) {
      console.error('Error authenticating user:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   * @param {string} id - User ID
   * @returns {Promise<Object>} - User
   */
  async getUserById(id) {
    try {
      const user = await User.findById(id);
      if (!user) {
        throw new Error('User not found');
      }
      return UserDTO.fromEntity(user);
    } catch (error) {
      console.error(`Error getting user with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update user
   * @param {string} id - User ID
   * @param {Object} userData - Updated user data
   * @returns {Promise<Object>} - Updated user
   */
  async updateUser(id, userData) {
    try {
      // Find user by ID
      const user = await User.findById(id);
      if (!user) {
        throw new Error('User not found');
      }

      // Update user fields
      if (userData.email) {
        user.email = userData.email.trim().toLowerCase();
      }

      if (userData.password) {
        user.password = userData.password;
      }

      if (userData.role) {
        user.role = userData.role;
      }

      // Save updated user
      await user.save();

      return UserDTO.fromEntity(user);
    } catch (error) {
      console.error(`Error updating user with ID ${id}:`, error);
      throw error;
    }
  }
}

// Create a singleton instance
const userService = new UserService();

module.exports = userService;
