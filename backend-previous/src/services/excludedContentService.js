const ExcludedContent = require('../models/excludedContent');
const { v4: uuidv4 } = require('uuid');

class ExcludedContentService {
  /**
   * Add content to exclusion list
   * @param {Object} contentData - Content to exclude
   * @param {string} reason - Reason for exclusion
   * @returns {Promise<Object>} - Created excluded content
   */
  async excludeContent(contentData, reason = 'Manual exclusion by admin') {
    try {
      const excludedContent = new ExcludedContent({
        id: uuidv4(),
        text_en: contentData.text_en,
        text_es: contentData.text_es,
        text_dom: contentData.text_dom,
        gameMode: contentData.gameMode,
        category: contentData.category,
        reason: reason,
        originalContentId: contentData.id || contentData._id,
        excludedAt: new Date()
      });

      const saved = await excludedContent.save();
      console.log(`Content excluded: ${contentData.gameMode}/${contentData.category} - "${contentData.text_es}"`);
      
      return saved;
    } catch (error) {
      console.error('Error excluding content:', error);
      throw error;
    }
  }

  /**
   * Get all excluded content for a specific game mode and category
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @returns {Promise<Array>} - Array of excluded content
   */
  async getExcludedContent(gameMode, category) {
    try {
      const excludedContent = await ExcludedContent.find({
        gameMode: gameMode,
        category: category
      }).sort({ excludedAt: -1 });

      console.log(`Found ${excludedContent.length} excluded items for ${gameMode}/${category}`);
      return excludedContent;
    } catch (error) {
      console.error('Error getting excluded content:', error);
      throw error;
    }
  }

  /**
   * Get all excluded content (for admin purposes)
   * @returns {Promise<Array>} - Array of all excluded content
   */
  async getAllExcludedContent() {
    try {
      const excludedContent = await ExcludedContent.find({})
        .sort({ excludedAt: -1 });

      return excludedContent;
    } catch (error) {
      console.error('Error getting all excluded content:', error);
      throw error;
    }
  }

  /**
   * Check if content is already excluded
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @param {string} textEs - Spanish text to check
   * @returns {Promise<boolean>} - True if content is excluded
   */
  async isContentExcluded(gameMode, category, textEs) {
    try {
      const existing = await ExcludedContent.findOne({
        gameMode: gameMode,
        category: category,
        text_es: textEs
      });

      return !!existing;
    } catch (error) {
      console.error('Error checking if content is excluded:', error);
      return false;
    }
  }

  /**
   * Remove content from exclusion list
   * @param {string} excludedContentId - ID of excluded content to remove
   * @returns {Promise<boolean>} - True if removed successfully
   */
  async removeExclusion(excludedContentId) {
    try {
      const result = await ExcludedContent.findByIdAndDelete(excludedContentId);
      
      if (result) {
        console.log(`Removed exclusion: ${result.gameMode}/${result.category} - "${result.text_es}"`);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error removing exclusion:', error);
      throw error;
    }
  }

  /**
   * Reset all excluded content (clear the entire collection)
   * @returns {Promise<Object>} - Result with count of deleted items
   */
  async resetAllExcludedContent() {
    try {
      const result = await ExcludedContent.deleteMany({});
      
      console.log(`Reset excluded content: ${result.deletedCount} items removed`);
      
      return {
        success: true,
        deletedCount: result.deletedCount,
        message: `Successfully removed ${result.deletedCount} excluded content items`
      };
    } catch (error) {
      console.error('Error resetting excluded content:', error);
      throw error;
    }
  }

  /**
   * Get excluded content statistics
   * @returns {Promise<Object>} - Statistics about excluded content
   */
  async getExclusionStats() {
    try {
      const stats = await ExcludedContent.aggregate([
        {
          $group: {
            _id: {
              gameMode: '$gameMode',
              category: '$category'
            },
            count: { $sum: 1 },
            latestExclusion: { $max: '$excludedAt' }
          }
        },
        {
          $sort: { 'latestExclusion': -1 }
        }
      ]);

      const totalCount = await ExcludedContent.countDocuments();

      return {
        totalExcluded: totalCount,
        byCategory: stats
      };
    } catch (error) {
      console.error('Error getting exclusion stats:', error);
      throw error;
    }
  }

  /**
   * Build exclusion prompt text for AI generation
   * @param {string} gameMode - questions or dares
   * @param {string} category - category name
   * @returns {Promise<string>} - Formatted exclusion text for prompt
   */
  async buildExclusionPrompt(gameMode, category) {
    try {
      const excludedContent = await this.getExcludedContent(gameMode, category);
      
      if (excludedContent.length === 0) {
        return '';
      }

      const excludedTexts = excludedContent.map(item => `- "${item.text_es}"`).join('\n');
      
      return `
🚫 EXCLUDED CONTENT - DO NOT GENERATE ANYTHING SIMILAR TO THESE:
The following content has been manually excluded and should NOT be generated or used as inspiration:

${excludedTexts}

IMPORTANT: Avoid generating any content that is similar in theme, wording, or concept to the above excluded examples. Be creative and generate completely different content.
`;
    } catch (error) {
      console.error('Error building exclusion prompt:', error);
      return '';
    }
  }
}

module.exports = new ExcludedContentService();
