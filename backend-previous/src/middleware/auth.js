const jwt = require('jsonwebtoken');

module.exports = (req, res, next) => {
  console.log('Auth middleware checking token...');

  // Get token from header - check both x-auth-token and Authorization header
  let token = req.header('x-auth-token');

  // If no x-auth-token, check Authorization header (Bearer token)
  if (!token) {
    const authHeader = req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7); // Remove 'Bearer ' prefix
      console.log('Using token from Authorization header');
    }
  } else {
    console.log('Using token from x-auth-token header');
  }

  // Check if we're in development mode and BYPASS_AUTH is enabled
  if (process.env.NODE_ENV === 'development' && process.env.BYPASS_AUTH === 'true') {
    console.log(`Development mode with BYPASS_AUTH enabled: Bypassing auth for ${req.method} request to ${req.originalUrl}`);

    // Use admin email from environment variable
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

    req.user = {
      email: adminEmail,
      role: 'admin',
      id: '123456789012345678901234'
    };
    console.log('Using mock admin user:', adminEmail);
    return next();
  }

  // Check if no token
  if (!token) {
    console.log('No token provided in request (checked both x-auth-token and Authorization headers)');
    return res.status(401).json({ message: 'No token, authorization denied' });
  }

  console.log('Token found:', token.substring(0, 15) + '...');

  try {
    // Check if JWT_SECRET is defined
    if (!process.env.JWT_SECRET) {
      console.error('ERROR: JWT_SECRET environment variable is not set!');
      return res.status(500).json({ message: 'Server configuration error' });
    }

    const jwtSecret = process.env.JWT_SECRET;

    // Verify token
    const decoded = jwt.verify(token, jwtSecret);
    console.log('Token verified, user role:', decoded.role);

    // Add user from payload
    req.user = decoded;
    next();
  } catch (error) {
    console.error('Token verification failed:', error.message);
    res.status(401).json({ message: 'Token is not valid' });
  }
};