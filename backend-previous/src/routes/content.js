const express = require('express');
const router = express.Router();
const contentController = require('../controllers/contentController');

/**
 * @swagger
 * tags:
 *   name: Content
 *   description: Public content endpoints
 */

/**
 * @swagger
 * /content:
 *   get:
 *     summary: Get all game content
 *     tags: [Content]
 *     description: Retrieves all active game content formatted for the mobile app. May return encrypted data if ENCRYPTION_KEY is set.
 *     responses:
 *       200:
 *         description: A structured object containing all game content or encrypted data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 questions:
 *                   type: object
 *                   properties:
 *                     casual_questions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           text_en:
 *                             type: string
 *                           text_es:
 *                             type: string
 *                     mild_questions:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Content'
 *                     spicy_questions:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Content'
 *                     no_limits_questions:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Content'
 *                 dares:
 *                   type: object
 *                   properties:
 *                     casual_dares:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Content'
 *                     mild_dares:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Content'
 *                     spicy_dares:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Content'
 *                     no_limits_dares:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Content'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', contentController.getAllContent);

module.exports = router;