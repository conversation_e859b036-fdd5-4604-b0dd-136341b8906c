const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const authMiddleware = require('../middleware/auth');

/**
 * @swagger
 * tags:
 *   name: Admin
 *   description: Admin authentication and content management
 */

/**
 * @swagger
 * /admin/login:
 *   post:
 *     summary: Admin login
 *     tags: [Admin]
 *     description: Authenticate admin user and get JWT token
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 format: password
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 token:
 *                   type: string
 *                   description: JWT token
 *                 user:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     email:
 *                       type: string
 *                     role:
 *                       type: string
 *       401:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/login', adminController.login);

// Public ad settings endpoint (for mobile app)
router.get('/ad-settings', adminController.getAdSettings);

// Protected admin routes
router.use(authMiddleware);

/**
 * @swagger
 * /admin/content:
 *   get:
 *     summary: Get all content (admin view)
 *     tags: [Admin]
 *     description: Retrieves all content items including inactive ones
 *     security:
 *       - bearerAuth: []
 *       - apiKeyAuth: []
 *     responses:
 *       200:
 *         description: Array of all content items
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Content'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   post:
 *     summary: Create new content
 *     tags: [Admin]
 *     description: Creates a new content item
 *     security:
 *       - bearerAuth: []
 *       - apiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text_en
 *               - text_es
 *               - category
 *               - gameMode
 *             properties:
 *               text_en:
 *                 type: string
 *               text_es:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [casual_questions, mild_questions, spicy_questions, no_limits_questions, casual_dares, mild_dares, spicy_dares, no_limits_dares]
 *               gameMode:
 *                 type: string
 *                 enum: [questions, dares]
 *               active:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       201:
 *         description: Content created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Content'
 *       400:
 *         description: Invalid input
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/content', adminController.getAllContent);
router.post('/content', adminController.createContent);
router.post('/content/generate', adminController.generateAIContent);
router.post('/content/bulk-create', adminController.bulkCreateAIContent);

// Analytics routes
router.get('/analytics', adminController.getAnalytics);

/**
 * @swagger
 * /admin/content/{id}:
 *   get:
 *     summary: Get content by ID
 *     tags: [Admin]
 *     description: Retrieves a specific content item by ID
 *     security:
 *       - bearerAuth: []
 *       - apiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Content ID
 *     responses:
 *       200:
 *         description: Content item
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Content'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Content not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   put:
 *     summary: Update content
 *     tags: [Admin]
 *     description: Updates a specific content item
 *     security:
 *       - bearerAuth: []
 *       - apiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Content ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               text_en:
 *                 type: string
 *               text_es:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [casual_questions, mild_questions, spicy_questions, no_limits_questions, casual_dares, mild_dares, spicy_dares, no_limits_dares]
 *               gameMode:
 *                 type: string
 *                 enum: [questions, dares]
 *               active:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Content updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Content'
 *       400:
 *         description: Invalid input
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Content not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   delete:
 *     summary: Delete content
 *     tags: [Admin]
 *     description: Deletes a specific content item
 *     security:
 *       - bearerAuth: []
 *       - apiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Content ID
 *     responses:
 *       200:
 *         description: Content deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Content not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/content/:id', adminController.getContentById);
router.put('/content/:id', adminController.updateContent);
router.delete('/content/:id', adminController.deleteContent);

/**
 * @swagger
 * /admin/penalties:
 *   get:
 *     summary: Get all penalties (admin view)
 *     tags: [Admin]
 *     description: Retrieves all penalty items including inactive ones
 *     security:
 *       - bearerAuth: []
 *       - apiKeyAuth: []
 *     responses:
 *       200:
 *         description: Array of all penalty items
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Penalty'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   post:
 *     summary: Create new penalty
 *     tags: [Admin]
 *     description: Creates a new penalty item
 *     security:
 *       - bearerAuth: []
 *       - apiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text_en
 *               - text_es
 *               - text_dom
 *               - category
 *             properties:
 *               text_en:
 *                 type: string
 *                 description: Penalty text in English
 *               text_es:
 *                 type: string
 *                 description: Penalty text in Spanish
 *               text_dom:
 *                 type: string
 *                 description: Penalty text in Dominican Spanish
 *               category:
 *                 type: string
 *                 enum: [drinking, physical, social, silly, creative]
 *                 description: Penalty category
 *               active:
 *                 type: boolean
 *                 default: true
 *                 description: Whether the penalty is active
 *               isPremium:
 *                 type: boolean
 *                 default: false
 *                 description: Whether the penalty is premium-only
 *               isDefaultFree:
 *                 type: boolean
 *                 default: false
 *                 description: Whether the penalty is default for free users
 *               isDefaultPremium:
 *                 type: boolean
 *                 default: false
 *                 description: Whether the penalty is default for premium users
 *     responses:
 *       201:
 *         description: Penalty created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Penalty'
 *       400:
 *         description: Invalid input
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/penalties', adminController.getAllPenalties);
router.post('/penalties', adminController.createPenalty);

/**
 * @swagger
 * /admin/penalties/{id}:
 *   get:
 *     summary: Get penalty by ID
 *     tags: [Admin]
 *     description: Retrieves a specific penalty item by ID
 *     security:
 *       - bearerAuth: []
 *       - apiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Penalty ID
 *     responses:
 *       200:
 *         description: Penalty item
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Penalty'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Penalty not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   put:
 *     summary: Update penalty
 *     tags: [Admin]
 *     description: Updates a specific penalty item
 *     security:
 *       - bearerAuth: []
 *       - apiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Penalty ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               text_en:
 *                 type: string
 *                 description: Penalty text in English
 *               text_es:
 *                 type: string
 *                 description: Penalty text in Spanish
 *               text_dom:
 *                 type: string
 *                 description: Penalty text in Dominican Spanish
 *               category:
 *                 type: string
 *                 enum: [drinking, physical, social, silly, creative]
 *                 description: Penalty category
 *               active:
 *                 type: boolean
 *                 description: Whether the penalty is active
 *               isPremium:
 *                 type: boolean
 *                 description: Whether the penalty is premium-only
 *               isDefaultFree:
 *                 type: boolean
 *                 description: Whether the penalty is default for free users
 *               isDefaultPremium:
 *                 type: boolean
 *                 description: Whether the penalty is default for premium users
 *     responses:
 *       200:
 *         description: Penalty updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Penalty'
 *       400:
 *         description: Invalid input
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Penalty not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   delete:
 *     summary: Delete penalty
 *     tags: [Admin]
 *     description: Deletes a specific penalty item
 *     security:
 *       - bearerAuth: []
 *       - apiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Penalty ID
 *     responses:
 *       200:
 *         description: Penalty deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Penalty not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/penalties/:id', adminController.getPenaltyById);
router.put('/penalties/:id', adminController.updatePenalty);
router.delete('/penalties/:id', adminController.deletePenalty);

// Ad settings update route (requires auth)
router.put('/ad-settings', adminController.updateAdSettings);

module.exports = router;