const express = require('express');
const router = express.Router();
const PushToken = require('../models/PushToken');
const authMiddleware = require('../middleware/auth');

/**
 * @swagger
 * components:
 *   schemas:
 *     PushToken:
 *       type: object
 *       required:
 *         - token
 *         - platform
 *       properties:
 *         token:
 *           type: string
 *           description: The Expo push token
 *         platform:
 *           type: string
 *           enum: [ios, android]
 *           description: The device platform
 *         deviceId:
 *           type: string
 *           description: Unique device identifier
 *         language:
 *           type: string
 *           enum: [en, es, dom]
 *           description: User's language preference
 *           default: en
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Token creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Token last update timestamp
 */

/**
 * @swagger
 * /api/push-tokens:
 *   post:
 *     summary: Register a push notification token
 *     tags: [Push Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - platform
 *             properties:
 *               token:
 *                 type: string
 *                 description: The Expo push token
 *               platform:
 *                 type: string
 *                 enum: [ios, android]
 *                 description: The device platform
 *               deviceId:
 *                 type: string
 *                 description: Unique device identifier
               language:
                 type: string
                 enum: [en, es, dom]
                 description: User's language preference
                 default: en
 *     responses:
 *       201:
 *         description: Token registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 token:
 *                   $ref: '#/components/schemas/PushToken'
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
router.post('/push-tokens', async (req, res) => {
  try {
    const { token, platform, deviceId, language, notificationEnabled } = req.body;

    // Validate required fields
    if (!token || !platform) {
      return res.status(400).json({
        message: 'Token and platform are required'
      });
    }

    // Validate platform
    if (!['ios', 'android'].includes(platform)) {
      return res.status(400).json({
        message: 'Platform must be either ios or android'
      });
    }

    // Validate language (optional, defaults to 'en')
    const validLanguages = ['en', 'es', 'dom'];
    const userLanguage = language && validLanguages.includes(language) ? language : 'en';

    // Check if token already exists
    let existingToken = await PushToken.findOne({ token });

    if (existingToken) {
      // Update existing token with new device info, language, and notification preference if provided
      let updated = false;
      if (deviceId && existingToken.deviceId !== deviceId) {
        existingToken.deviceId = deviceId;
        updated = true;
      }
      if (userLanguage && existingToken.language !== userLanguage) {
        existingToken.language = userLanguage;
        updated = true;
      }
      if (typeof notificationEnabled === 'boolean' && existingToken.notificationEnabled !== notificationEnabled) {
        existingToken.notificationEnabled = notificationEnabled;
        updated = true;
      }

      if (updated) {
        existingToken.updatedAt = new Date();
        await existingToken.save();
      }

      return res.status(200).json({
        message: 'Token already registered',
        token: existingToken
      });
    }

    // Create new token
    const newToken = new PushToken({
      token,
      platform,
      deviceId: deviceId || 'unknown',
      language: userLanguage,
      notificationEnabled: typeof notificationEnabled === 'boolean' ? notificationEnabled : true
    });

    await newToken.save();

    res.status(201).json({
      message: 'Push token registered successfully',
      token: newToken
    });

  } catch (error) {
    console.error('Error registering push token:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/admin/push-tokens:
 *   get:
 *     summary: Get all registered push tokens (Admin only)
 *     tags: [Push Notifications, Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of all push tokens
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 tokens:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/PushToken'
 *                 total:
 *                   type: number
 *                   description: Total number of tokens
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/admin/push-tokens', authMiddleware, async (req, res) => {
  try {
    const tokens = await PushToken.find().sort({ createdAt: -1 });

    res.status(200).json({
      tokens,
      total: tokens.length
    });

  } catch (error) {
    console.error('Error fetching push tokens:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/admin/push-notifications/send:
 *   post:
 *     summary: Send push notifications to all registered devices (Admin only)
 *     tags: [Push Notifications, Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - body
 *             properties:
 *               title:
 *                 type: string
 *                 description: Notification title
 *               body:
 *                 type: string
 *                 description: Notification body
 *               data:
 *                 type: object
 *                 description: Additional data to send with notification
               language:
                 type: string
                 enum: [en, es, dom]
                 description: Target language filter (optional, sends to all if not specified)
               forcePush:
                 type: boolean
                 description: Send to all active devices regardless of notification preferences (optional, default false)
 *     responses:
 *       200:
 *         description: Notifications sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 sent:
 *                   type: number
 *                   description: Number of notifications sent
 *                 failed:
 *                   type: number
 *                   description: Number of failed notifications
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/admin/push-notifications/send', authMiddleware, async (req, res) => {
  try {
    console.log('📢 [Broadcast] Request received:', req.body);
    const { title, body, data, language, forcePush } = req.body;

    // Validate required fields
    if (!title || !body) {
      console.log('❌ [Broadcast] Validation failed:', { title: !!title, body: !!body });
      return res.status(400).json({
        message: 'Title and body are required'
      });
    }

    // Validate language filter (optional)
    const validLanguages = ['en', 'es', 'dom'];
    let languageFilter = null;
    if (language && validLanguages.includes(language)) {
      languageFilter = language;
    }

    // Get active push tokens, optionally filtered by language and notification preference
    const query = { isActive: true };

    // Only filter by notificationEnabled if not force push
    if (!forcePush) {
      query.notificationEnabled = true;
    }

    if (languageFilter) {
      query.language = languageFilter;
    }

    const tokens = await PushToken.find(query);
    console.log('🔍 [Broadcast] Query used:', query);
    console.log('📊 [Broadcast] Tokens found:', tokens.length);
    console.log('📋 [Broadcast] Token details:', tokens.map(t => ({
      deviceId: t.deviceId,
      platform: t.platform,
      language: t.language,
      notificationEnabled: t.notificationEnabled,
      isActive: t.isActive
    })));

    if (tokens.length === 0) {
      let message;
      if (languageFilter && forcePush) {
        message = `No active devices found for language: ${languageFilter}`;
      } else if (languageFilter) {
        message = `No devices with notifications enabled found for language: ${languageFilter}`;
      } else if (forcePush) {
        message = 'No active devices to send notifications to';
      } else {
        message = 'No devices with notifications enabled to send notifications to';
      }

      return res.status(200).json({
        message,
        sent: 0,
        failed: 0,
        language: languageFilter,
        forcePush: !!forcePush
      });
    }

    // Prepare notification messages
    const messages = tokens.map(tokenDoc => ({
      to: tokenDoc.token,
      sound: 'default',
      title,
      body,
      data: data || {}
    }));

    console.log('📤 [Broadcast] Sending to Expo:', messages.length, 'messages');
    console.log('📝 [Broadcast] Message sample:', messages[0]);

    // Function to divide messages into batches of 100
    function chunkArray(array, chunkSize = 100) {
      const chunks = [];
      for (let i = 0; i < array.length; i += chunkSize) {
        chunks.push(array.slice(i, i + chunkSize));
      }
      return chunks;
    }

    // Divide messages into batches
    const messageChunks = chunkArray(messages, 100);
    console.log(`📦 [Broadcast] Divided into ${messageChunks.length} batches of max 100 messages`);

    let totalSent = 0;
    let totalFailed = 0;
    const batchResults = [];

    // Send each batch
    for (let i = 0; i < messageChunks.length; i++) {
      const chunk = messageChunks[i];
      const batchNumber = i + 1;

      console.log(`📤 [Broadcast] Sending batch ${batchNumber}/${messageChunks.length} (${chunk.length} messages)`);
      console.log(`📝 [Broadcast] Batch ${batchNumber} messages:`, JSON.stringify(chunk, null, 2));

      try {
        const response = await fetch('https://exp.host/--/api/v2/push/send', {
          method: 'POST',
          headers: {
            Accept: 'application/json',
            'Accept-encoding': 'gzip, deflate',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(chunk),
        });

        console.log(`📡 [Broadcast] Batch ${batchNumber} HTTP status:`, response.status, response.statusText);
        const result = await response.json();
        console.log(`📥 [Broadcast] Batch ${batchNumber} response:`, JSON.stringify(result, null, 2));

        // Count results for this batch
        let batchSent = 0;
        let batchFailed = 0;

        if (Array.isArray(result)) {
          result.forEach((item, index) => {
            if (item.status === 'ok') {
              batchSent++;
            } else {
              batchFailed++;
              console.error(`❌ [Broadcast] Batch ${batchNumber} failed ${index + 1}:`, item);
            }
          });
        } else if (result.status === 'ok') {
          batchSent = 1;
        } else {
          batchFailed = 1;
          console.error(`❌ [Broadcast] Batch ${batchNumber} failed:`, result);
        }

        totalSent += batchSent;
        totalFailed += batchFailed;

        batchResults.push({
          batch: batchNumber,
          sent: batchSent,
          failed: batchFailed,
          total: chunk.length
        });

        console.log(`✅ [Broadcast] Batch ${batchNumber} completed: ${batchSent} sent, ${batchFailed} failed`);

        // Small delay between batches to avoid rate limiting
        if (i < messageChunks.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

      } catch (error) {
        console.error(`❌ [Broadcast] Batch ${batchNumber} error:`, error);
        totalFailed += chunk.length;
        batchResults.push({
          batch: batchNumber,
          sent: 0,
          failed: chunk.length,
          total: chunk.length,
          error: error.message
        });
      }
    }

    console.log(`📊 [Broadcast] Final count: ${totalSent} sent, ${totalFailed} failed`);

    let message;
    const forceText = forcePush ? ' (FORCE PUSH)' : '';
    const batchText = messageChunks.length > 1 ? ` in ${messageChunks.length} batches` : '';

    if (languageFilter) {
      message = `Notifications sent to ${languageFilter} users${forceText}${batchText}: ${totalSent} successful, ${totalFailed} failed`;
    } else {
      message = `Notifications sent${forceText}${batchText}: ${totalSent} successful, ${totalFailed} failed`;
    }

    res.status(200).json({
      message,
      sent: totalSent,
      failed: totalFailed,
      language: languageFilter,
      forcePush: !!forcePush,
      totalTargeted: tokens.length,
      batches: messageChunks.length,
      batchResults: batchResults
    });

  } catch (error) {
    console.error('Error sending push notifications:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/admin/push-notifications/send-specific:
 *   post:
 *     summary: Send push notification to a specific device (Admin only)
 *     tags: [Push Notifications, Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - body
 *               - deviceId
 *             properties:
 *               title:
 *                 type: string
 *                 description: Notification title
 *               body:
 *                 type: string
 *                 description: Notification body
 *               data:
 *                 type: object
 *                 description: Additional data to send with notification
 *               deviceId:
 *                 type: string
 *                 description: Target device ID
 *     responses:
 *       200:
 *         description: Notification sent successfully
 *       400:
 *         description: Invalid request data
 *       404:
 *         description: Device not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/admin/push-notifications/send-specific', authMiddleware, async (req, res) => {
  try {
    console.log('🎯 [Send Specific] Request received:', req.body);
    const { title, body, data, deviceId } = req.body;

    // Validate required fields
    if (!title || !body || !deviceId) {
      console.log('❌ [Send Specific] Validation failed:', { title: !!title, body: !!body, deviceId: !!deviceId });
      return res.status(400).json({
        message: 'Title, body, and device ID are required'
      });
    }

    // Find the specific device token
    console.log('🔍 [Send Specific] Looking for device:', deviceId.trim());
    const token = await PushToken.findOne({
      deviceId: deviceId.trim(),
      isActive: true
    });

    if (!token) {
      console.log('❌ [Send Specific] Device not found:', deviceId);
      return res.status(404).json({
        message: `Device not found: ${deviceId}`
      });
    }

    console.log('✅ [Send Specific] Device found:', { deviceId: token.deviceId, platform: token.platform });

    // Prepare notification message
    const message = {
      to: token.token,
      sound: 'default',
      title,
      body,
      data: data || {}
    };

    // Send notification using Expo's push notification service
    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    const result = await response.json();

    // Check if notification was sent successfully
    let success = false;
    let errorMessage = null;

    if (result.status === 'ok') {
      success = true;
    } else {
      errorMessage = result.message || 'Failed to send notification';
      console.error('Failed to send specific notification:', result);
    }

    const responseMessage = success
      ? `Notification sent successfully to device: ${deviceId}`
      : `Failed to send notification to device: ${deviceId} - ${errorMessage}`;

    res.status(success ? 200 : 400).json({
      message: responseMessage,
      success,
      deviceId,
      platform: token.platform,
      language: token.language,
      notificationEnabled: token.notificationEnabled
    });

  } catch (error) {
    console.error('Error sending specific push notification:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/push-tokens/preference:
 *   put:
 *     summary: Update notification preference for a device
 *     tags: [Push Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - deviceId
 *               - notificationEnabled
 *             properties:
 *               deviceId:
 *                 type: string
 *                 description: Unique device identifier
 *               notificationEnabled:
 *                 type: boolean
 *                 description: Whether notifications are enabled for this device
 *     responses:
 *       200:
 *         description: Notification preference updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 updated:
 *                   type: boolean
 *       404:
 *         description: Device not found
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
router.put('/push-tokens/preference', async (req, res) => {
  try {
    const { deviceId, notificationEnabled } = req.body;

    // Validate required fields
    if (!deviceId || typeof notificationEnabled !== 'boolean') {
      return res.status(400).json({
        message: 'Device ID and notification preference (boolean) are required'
      });
    }

    // Find and update the token by device ID
    const updatedToken = await PushToken.findOneAndUpdate(
      { deviceId },
      {
        notificationEnabled,
        updatedAt: new Date()
      },
      { new: true }
    );

    if (!updatedToken) {
      return res.status(404).json({
        message: 'Device not found'
      });
    }

    res.status(200).json({
      message: 'Notification preference updated successfully',
      updated: true,
      notificationEnabled: updatedToken.notificationEnabled
    });

  } catch (error) {
    console.error('Error updating notification preference:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
