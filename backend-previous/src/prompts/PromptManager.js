/**
 * Prompt Manager
 *
 * Central manager for all AI content generation prompts.
 * Handles loading, combining, and building prompts for different categories and game modes.
 */

const basePrompt = require('./base/basePrompt');

// Import all category-specific prompts
const casualQuestions = require('./categories/casual_questions');
const mildQuestions = require('./categories/mild_questions');
const spicyQuestions = require('./categories/spicy_questions');
const noLimitsQuestions = require('./categories/no_limits_questions');
const coupleQuestions = require('./categories/couple_questions');
const casualDares = require('./categories/casual_dares');
const mildDares = require('./categories/mild_dares');
const spicyDares = require('./categories/spicy_dares');
const noLimitsDares = require('./categories/no_limits_dares');
const coupleDares = require('./categories/couple_dares');

class PromptManager {
  constructor() {
    // Map of all available category prompts
    this.categoryPrompts = {
      'casual_questions': casualQuestions,
      'mild_questions': mildQuestions,
      'spicy_questions': spicyQuestions,
      'no_limits_questions': noLimitsQuestions,
      'couple_questions': coupleQuestions,
      'casual_dares': casualDares,
      'mild_dares': mildDares,
      'spicy_dares': spicyDares,
      'no_limits_dares': noLimitsDares,
      'couple_dares': coupleDares
    };

    // Valid game modes and categories
    this.validGameModes = ['questions', 'dares'];
    this.validCategories = Object.keys(this.categoryPrompts);
  }

  /**
   * Check if a category is valid
   * @param {string} category - Category to validate
   * @returns {boolean} - Whether the category is valid
   */
  isValidCategory(category) {
    return this.validCategories.includes(category);
  }

  /**
   * Check if a game mode is valid
   * @param {string} gameMode - Game mode to validate
   * @returns {boolean} - Whether the game mode is valid
   */
  isValidGameMode(gameMode) {
    return this.validGameModes.includes(gameMode);
  }

  /**
   * Get category prompt configuration
   * @param {string} category - Category name
   * @returns {Object|null} - Category prompt configuration or null if not found
   */
  getCategoryPrompt(category) {
    return this.categoryPrompts[category] || null;
  }

  /**
   * Get all available categories for a game mode
   * @param {string} gameMode - Game mode (questions or dares)
   * @returns {Array} - Array of available categories
   */
  getCategoriesForGameMode(gameMode) {
    return this.validCategories.filter(category => category.includes(gameMode));
  }

  /**
   * Get category metadata
   * @param {string} category - Category name
   * @returns {Object|null} - Category metadata or null if not found
   */
  getCategoryMetadata(category) {
    const categoryPrompt = this.getCategoryPrompt(category);
    return categoryPrompt ? categoryPrompt.metadata : null;
  }

  /**
   * Get example content for a category
   * @param {string} category - Category name
   * @returns {Array} - Array of example content
   */
  getCategoryExamples(category) {
    const categoryPrompt = this.getCategoryPrompt(category);
    return categoryPrompt ? categoryPrompt.getExamples() : [];
  }

  /**
   * Validate content against category requirements
   * @param {Object} content - Content to validate
   * @param {string} category - Category name
   * @returns {boolean} - Whether content is valid for the category
   */
  validateContentForCategory(content, category) {
    const categoryPrompt = this.getCategoryPrompt(category);
    if (!categoryPrompt || !categoryPrompt.validateContent) {
      return true; // Default to valid if no validation function
    }
    return categoryPrompt.validateContent(content);
  }

  /**
   * Build a complete prompt for content generation
   * @param {string} gameMode - Game mode (questions or dares)
   * @param {string} category - Category name
   * @param {Array} existingContent - Array of existing content for context
   * @param {Object} options - Additional options
   * @returns {Object} - Complete prompt configuration
   */
  buildPrompt(gameMode, category, existingContent = [], options = {}) {
    // Validate inputs
    if (!this.isValidGameMode(gameMode)) {
      throw new Error(`Invalid game mode: ${gameMode}. Valid options: ${this.validGameModes.join(', ')}`);
    }

    if (!this.isValidCategory(category)) {
      throw new Error(`Invalid category: ${category}. Valid options: ${this.validCategories.join(', ')}`);
    }

    // Get category-specific prompt
    const categoryPrompt = this.getCategoryPrompt(category);
    if (!categoryPrompt) {
      throw new Error(`Category prompt not found: ${category}`);
    }

    // Extract options
    const {
      count = basePrompt.generationParams.defaultCount,
      includeExamples = true,
      useExistingContent = true
    } = options;

    // Determine content type for display
    const contentType = gameMode === 'questions' ? 'questions' : 'dares';
    const categoryDescription = categoryPrompt.metadata.description;

    // Build the complete prompt
    const promptParts = [];

    // 1. Base prompt with core requirements
    promptParts.push(basePrompt.buildBasePrompt({
      contentType,
      categoryDescription,
      count
    }));

    // 2. Category-specific guidelines
    promptParts.push(categoryPrompt.buildCategoryPrompt());

    // 3. Dominican language guidelines
    const categoryName = category.split('_')[0]; // Extract category name (casual, mild, spicy, no_limits)
    const dominicanGuidelines = basePrompt.buildDominicanGuidelines(categoryName);
    if (dominicanGuidelines) {
      promptParts.push(dominicanGuidelines);
    }

    // 4. Context from existing content
    if (useExistingContent && existingContent.length > 0) {
      promptParts.push(basePrompt.buildContextSection(existingContent));
    }

    // 5. Category examples (if requested and no existing content)
    if (includeExamples && (!useExistingContent || existingContent.length === 0)) {
      const examples = categoryPrompt.getExamples();
      if (examples.length > 0) {
        promptParts.push(basePrompt.buildContextSection(examples));
      }
    }

    // 6. Output format instructions
    promptParts.push(basePrompt.buildOutputSection(contentType, count));

    // Combine all parts
    const fullPrompt = promptParts.join('\n');

    return {
      systemMessage: basePrompt.systemMessage,
      userPrompt: fullPrompt,
      generationParams: {
        ...basePrompt.generationParams,
        ...options.generationParams
      },
      metadata: {
        gameMode,
        category,
        categoryDescription,
        contentType,
        count,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * Get system message for OpenAI
   * @returns {string} - System message
   */
  getSystemMessage() {
    return basePrompt.systemMessage;
  }

  /**
   * Get generation parameters
   * @param {Object} overrides - Parameter overrides
   * @returns {Object} - Generation parameters
   */
  getGenerationParams(overrides = {}) {
    return {
      ...basePrompt.generationParams,
      ...overrides
    };
  }

  /**
   * Get all available categories with metadata
   * @returns {Object} - Object with category names as keys and metadata as values
   */
  getAllCategoriesMetadata() {
    const metadata = {};
    for (const category of this.validCategories) {
      metadata[category] = this.getCategoryMetadata(category);
    }
    return metadata;
  }

  /**
   * Get prompt statistics
   * @returns {Object} - Statistics about available prompts
   */
  getPromptStats() {
    const stats = {
      totalCategories: this.validCategories.length,
      gameModesCount: this.validGameModes.length,
      categoriesByGameMode: {},
      totalExamples: 0
    };

    // Count categories by game mode
    for (const gameMode of this.validGameModes) {
      const categories = this.getCategoriesForGameMode(gameMode);
      stats.categoriesByGameMode[gameMode] = {
        count: categories.length,
        categories: categories
      };
    }

    // Count total examples
    for (const category of this.validCategories) {
      const examples = this.getCategoryExamples(category);
      stats.totalExamples += examples.length;
    }

    return stats;
  }

  /**
   * Validate a complete prompt configuration
   * @param {string} gameMode - Game mode
   * @param {string} category - Category
   * @returns {Object} - Validation result
   */
  validatePromptConfiguration(gameMode, category) {
    const errors = [];
    const warnings = [];

    // Validate game mode
    if (!this.isValidGameMode(gameMode)) {
      errors.push(`Invalid game mode: ${gameMode}`);
    }

    // Validate category
    if (!this.isValidCategory(category)) {
      errors.push(`Invalid category: ${category}`);
    }

    // Check if category matches game mode
    if (this.isValidCategory(category) && this.isValidGameMode(gameMode)) {
      if (!category.includes(gameMode)) {
        errors.push(`Category ${category} does not match game mode ${gameMode}`);
      }
    }

    // Check if category prompt exists and is complete
    const categoryPrompt = this.getCategoryPrompt(category);
    if (categoryPrompt) {
      if (!categoryPrompt.metadata) {
        warnings.push(`Category ${category} is missing metadata`);
      }
      if (!categoryPrompt.examples || categoryPrompt.examples.length === 0) {
        warnings.push(`Category ${category} has no examples`);
      }
      if (!categoryPrompt.buildCategoryPrompt) {
        errors.push(`Category ${category} is missing buildCategoryPrompt method`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

module.exports = new PromptManager();
