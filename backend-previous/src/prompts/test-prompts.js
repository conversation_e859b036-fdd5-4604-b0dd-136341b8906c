/**
 * Test script for the new modular prompt system
 * 
 * This script tests the PromptManager and category-specific prompts
 * to ensure everything is working correctly.
 */

const PromptManager = require('./PromptManager');

console.log('🧪 Testing TapTrap Modular Prompt System\n');

// Test 1: Basic PromptManager functionality
console.log('1️⃣ Testing PromptManager basic functionality...');
try {
  const stats = PromptManager.getPromptStats();
  console.log('✅ Prompt statistics:', stats);
  
  const allCategories = PromptManager.getAllCategoriesMetadata();
  console.log('✅ All categories loaded:', Object.keys(allCategories).length);
} catch (error) {
  console.error('❌ Error in basic functionality:', error.message);
}

// Test 2: Validation
console.log('\n2️⃣ Testing validation...');
try {
  // Valid configuration
  const validResult = PromptManager.validatePromptConfiguration('questions', 'casual_questions');
  console.log('✅ Valid configuration test:', validResult.isValid ? 'PASSED' : 'FAILED');
  
  // Invalid configuration
  const invalidResult = PromptManager.validatePromptConfiguration('invalid', 'invalid_category');
  console.log('✅ Invalid configuration test:', !invalidResult.isValid ? 'PASSED' : 'FAILED');
} catch (error) {
  console.error('❌ Error in validation:', error.message);
}

// Test 3: Prompt building for each category
console.log('\n3️⃣ Testing prompt building for all categories...');
const testCategories = [
  'casual_questions',
  'mild_questions', 
  'spicy_questions',
  'no_limits_questions',
  'casual_dares',
  'mild_dares',
  'spicy_dares',
  'no_limits_dares'
];

const mockExistingContent = [
  {
    text_en: "What's your favorite color?",
    text_es: "¿Cuál es tu color favorito?",
    text_dom: "¿Cuál e' tu color favorito?"
  }
];

for (const category of testCategories) {
  try {
    const gameMode = category.includes('questions') ? 'questions' : 'dares';
    const promptConfig = PromptManager.buildPrompt(gameMode, category, mockExistingContent);
    
    console.log(`✅ ${category}: Prompt built successfully`);
    console.log(`   - System message length: ${promptConfig.systemMessage.length}`);
    console.log(`   - User prompt length: ${promptConfig.userPrompt.length}`);
    console.log(`   - Category description: ${promptConfig.metadata.categoryDescription}`);
    
    // Test content validation if available
    const categoryPrompt = PromptManager.getCategoryPrompt(category);
    if (categoryPrompt && categoryPrompt.validateContent) {
      const testContent = { text_en: "test content", text_es: "contenido de prueba", text_dom: "contenido de prueba" };
      const isValid = PromptManager.validateContentForCategory(testContent, category);
      console.log(`   - Content validation available: ${isValid ? 'PASSED' : 'FAILED'}`);
    }
    
  } catch (error) {
    console.error(`❌ ${category}: Error building prompt:`, error.message);
  }
}

// Test 4: Category examples
console.log('\n4️⃣ Testing category examples...');
for (const category of testCategories) {
  try {
    const examples = PromptManager.getCategoryExamples(category);
    console.log(`✅ ${category}: ${examples.length} examples available`);
    
    if (examples.length > 0) {
      const firstExample = examples[0];
      const hasAllLanguages = firstExample.text_en && firstExample.text_es && firstExample.text_dom;
      console.log(`   - Multi-language support: ${hasAllLanguages ? 'PASSED' : 'FAILED'}`);
    }
  } catch (error) {
    console.error(`❌ ${category}: Error getting examples:`, error.message);
  }
}

// Test 5: Sample prompt output
console.log('\n5️⃣ Sample prompt output for casual_questions...');
try {
  const promptConfig = PromptManager.buildPrompt('questions', 'casual_questions', mockExistingContent);
  console.log('📝 Sample System Message:');
  console.log(promptConfig.systemMessage);
  console.log('\n📝 Sample User Prompt (first 500 chars):');
  console.log(promptConfig.userPrompt.substring(0, 500) + '...');
  console.log('\n📝 Generation Parameters:');
  console.log(JSON.stringify(promptConfig.generationParams, null, 2));
} catch (error) {
  console.error('❌ Error generating sample prompt:', error.message);
}

console.log('\n🎉 Prompt system testing completed!');
console.log('\n📊 Summary:');
console.log(`- Total categories: ${testCategories.length}`);
console.log(`- Game modes: ${PromptManager.validGameModes.length}`);
console.log(`- System ready for AI content generation`);
