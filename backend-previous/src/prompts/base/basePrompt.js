/**
 * Base Prompt Template for TapTrap AI Content Generation
 *
 * This template provides the foundational structure and requirements
 * that apply to all content generation requests, regardless of category or game mode.
 */

const dominicanDictionary = require('./dominicanDictionary');

const basePrompt = {
  /**
   * System message that defines the AI's role and general behavior
   */
  systemMessage: "You are a creative content generator for TapTrap, a party game app. You specialize in creating engaging, appropriate, and culturally sensitive content for different audiences and difficulty levels. Always respond with valid JSON arrays containing the requested content.",

  /**
   * Core requirements that apply to all content generation
   */
  coreRequirements: [
    "Content must be appropriate for the specified category level",
    "Each item should be unique and engaging",
    "Provide content in 3 languages: English, Spanish, and Dominican Spanish",
    "Keep content concise but interesting (aim for 10-50 words per item)",
    "Avoid repetition with existing content",
    "Ensure cultural sensitivity across all languages",
    "Maintain consistency in tone and style within the category"
  ],

  /**
   * Multi-language guidelines
   */
  languageGuidelines: {
    english: "Use clear, natural English that's easy to understand",
    spanish: "Use standard Spanish that's accessible to all Spanish speakers",
    dominican: "Use Dominican Spanish with local expressions and colloquialisms, but keep it understandable"
  },

  /**
   * General category level descriptions
   */
  categoryLevels: {
    casual: "Curious, reflexive or funny questions that invite open conversation, without putting the player at risk",
    mild: "Bold, uncomfortable or challenging questions/dares that provoke laughter or tension, without being sexual",
    spicy: "Direct, short, morbid and Dominican. Like coming from a mind that lives messing around. Uses dirty and daring words",
    no_limits: "Super vulgar, third person, with dirty and daring Dominican words like singar, ñema, toto, mamao, culo, pilonear, etc. One sentence max, no explanations"
  },

  /**
   * Content quality standards
   */
  qualityStandards: [
    "Avoid offensive, discriminatory, or harmful content",
    "Ensure content is inclusive and respectful",
    "Make content engaging and fun",
    "Avoid overly complex or confusing language",
    "Ensure translations are natural and culturally appropriate",
    "Maintain the spirit and intent across all language versions"
  ],

  /**
   * JSON format specification
   */
  outputFormat: {
    description: "Always return content in the following JSON array format",
    structure: {
      type: "array",
      items: {
        type: "object",
        properties: {
          text_en: {
            type: "string",
            description: "English version of the content"
          },
          text_es: {
            type: "string",
            description: "Spanish version of the content"
          },
          text_dom: {
            type: "string",
            description: "Dominican Spanish version of the content"
          }
        },
        required: ["text_en", "text_es", "text_dom"]
      }
    },
    example: [
      {
        "text_en": "What's your favorite childhood memory?",
        "text_es": "¿Cuál es tu recuerdo favorito de la infancia?",
        "text_dom": "¿Cuál e' tu recuerdo favorito de cuando eras pequeño?"
      }
    ]
  },

  /**
   * Content generation parameters
   */
  generationParams: {
    defaultCount: 8,
    maxTokens: 2000,
    temperature: 0.8,
    model: "gpt-4o-mini"
  },

  /**
   * Build the base prompt structure
   * @param {Object} options - Configuration options
   * @returns {string} - Formatted base prompt
   */
  buildBasePrompt: function(options = {}) {
    const {
      contentType = "content",
      categoryDescription = "general",
      count = this.generationParams.defaultCount
    } = options;

    return `You are a creative content generator for TapTrap, a party game app. Generate ${count} new ${contentType} for the "${categoryDescription}" category.

Requirements:
${this.coreRequirements.map(req => `- ${req}`).join('\n')}

Language Guidelines:
- English: ${this.languageGuidelines.english}
- Spanish: ${this.languageGuidelines.spanish}
- Dominican Spanish: ${this.languageGuidelines.dominican}

Quality Standards:
${this.qualityStandards.map(standard => `- ${standard}`).join('\n')}

`;
  },

  /**
   * Build the context section with existing content examples
   * @param {Array} existingContent - Array of existing content for context
   * @returns {string} - Formatted context section
   */
  buildContextSection: function(existingContent = []) {
    if (existingContent.length === 0) {
      return "";
    }

    let contextSection = `Here are some examples of existing content in this category for reference (DO NOT copy these):\n\n`;

    existingContent.slice(0, 5).forEach((item, index) => {
      contextSection += `Example ${index + 1}:\n`;
      contextSection += `English: ${item.text_en}\n`;
      contextSection += `Spanish: ${item.text_es}\n`;
      contextSection += `Dominican: ${item.text_dom}\n\n`;
    });

    return contextSection;
  },

  /**
   * Build the output format section
   * @param {string} contentType - Type of content being generated
   * @param {number} count - Number of items to generate
   * @returns {string} - Formatted output format section
   */
  buildOutputSection: function(contentType = "items", count = 8) {
    return `Please generate ${count} new ${contentType} in the following JSON format:
[
  {
    "text_en": "English version",
    "text_es": "Spanish version",
    "text_dom": "Dominican Spanish version"
  }
]

Make sure the JSON is valid and properly formatted. Focus on creativity and variety while maintaining the appropriate tone for the category.`;
  },

  /**
   * Build Dominican language guidelines for specific category
   * @param {string} category - Category name (casual, mild, spicy, no_limits)
   * @returns {string} - Dominican language guidelines
   */
  buildDominicanGuidelines: function(category) {
    return dominicanDictionary.buildDominicanPrompt(category);
  }
};

module.exports = basePrompt;
