/**
 * Couple Questions Prompt Configuration
 *
 * Prompts and guidelines specifically for generating couple questions
 * Content designed for romantic partners and couples
 */

const coupleQuestions = {
  /**
   * Category metadata
   */
  metadata: {
    category: "couple_questions",
    gameMode: "questions",
    difficulty: "couple",
    description: "Couple Questions - Content specifically designed for couples and romantic partners"
  },

  /**
   * Specific guidelines for couple questions
   */
  guidelines: [
    "MUST be in third person directed at the group (¿Quién del grupo crees que...?, ¿Quién coro crees que...?)",
    "Include romantic and relationship-focused questions",
    "Include hypothetical romantic scenarios (<PERSON> tuvieras..., <PERSON> pudier<PERSON>..., Si fueras..., <PERSON> estuvieras...)",
    "Focus on romantic relationships, dating, and couple dynamics",
    "One sentence maximum, no explanations or embellishments",
    "Use varied question starters: ¿Quién del grupo, ¿Quién coro, <PERSON> tuvier<PERSON>, <PERSON> pudier<PERSON>, <PERSON> estuvier<PERSON>",
    "Make questions that couples can relate to and discuss together",
    "Include questions about romantic preferences and relationship dynamics",
    "Mix personal romantic questions with group dynamics questions and romantic hypothetical scenarios",
    "Questions should be intimate but not explicitly sexual"
  ],

  /**
   * Tone and style requirements
   */
  toneAndStyle: {
    tone: "Romantic, intimate, playful",
    style: "Dominican casual language with romantic focus",
    mood: "Loving, curious, relationship-focused",
    complexity: "Simple but meaningful for couples",
    length: "One sentence maximum"
  },

  /**
   * Content themes and topics
   */
  themes: [
    "Romantic gestures and expressions",
    "Dating preferences and experiences",
    "Relationship dynamics and communication",
    "Romantic fantasies and dreams",
    "Love languages and affection",
    "Couple activities and adventures",
    "Romantic milestones and memories",
    "Partner preferences and attraction",
    "Relationship goals and future plans",
    "Romantic surprises and gifts",
    "Intimacy and emotional connection",
    "Couple challenges and growth"
  ],

  /**
   * Content examples for inspiration (DO NOT COPY THESE - they are for reference only)
   */
  examples: [
    {
      text_en: "Who in the group do you think would be the most romantic partner?",
      text_es: "¿Quién del grupo crees que sería la pareja más romántica?",
      text_dom: "¿Quién del grupo tú cree que sería la pareja má' romántica?"
    },
    {
      text_en: "Who in the group do you think would plan the most creative date?",
      text_es: "¿Quién del grupo crees que planificaría la cita más creativa?",
      text_dom: "¿Quién del grupo tú cree que planificaría la cita má' creativa?"
    },
    {
      text_en: "If you could go on a romantic getaway anywhere with your partner, where would it be?",
      text_es: "Si pudieras ir a una escapada romántica a cualquier lugar con tu pareja, ¿dónde sería?",
      text_dom: "Si pudieras ir a una escapada romántica a cualquier lugar con tu pareja, ¿dónde sería?"
    },
    {
      text_en: "Who in the group do you think would be the best at expressing their feelings?",
      text_es: "¿Quién del grupo crees que sería mejor expresando sus sentimientos?",
      text_dom: "¿Quién del grupo tú cree que sería mejor expresando sus sentimientos?"
    }
  ],

  /**
   * Things to avoid for this category
   */
  avoidTopics: [
    "Explicitly sexual content (use spicy category for that)",
    "Conversations between two people (nothing like '¿tú también...?', '¿soy la única que...?')",
    "Very long or embellished phrases",
    "Generic relationship questions without Dominican flavor",
    "Non-consensual activities",
    "Harmful or toxic relationship dynamics",
    "Overly personal or invasive questions",
    "Questions that could cause relationship conflicts"
  ],

  /**
   * Special considerations for couple questions
   */
  specialConsiderations: [
    "Questions should be in third person and apply to couples",
    "Use Dominican casual language but keep it romantic",
    "One sentence maximum, no explanations or embellishments",
    "Use varied question starters: ¿Quién del grupo, ¿Quién coro, Si tuvieras, Si pudieras",
    "Focus on positive relationship dynamics",
    "Make questions that strengthen couple bonds",
    "Include both serious and playful romantic topics",
    "Language should be Dominican but accessible to couples"
  ],

  /**
   * Language-specific guidelines
   */
  languageSpecific: {
    english: {
      style: "Romantic, casual but clear for B2 English users",
      tone: "Loving, intimate, playful",
      considerations: ["Use romantic language", "Keep it couple-focused", "Make it engaging"]
    },
    spanish: {
      style: "Standard Spanish with romantic terms",
      tone: "Romantic and intimate",
      considerations: ["Use romantic language", "Keep it couple-focused", "Make it meaningful"]
    },
    dominican: {
      style: "Dominican casual with romantic focus",
      tone: "Loving, playful, intimate",
      considerations: ["Use Dominican expressions", "Keep romantic focus", "Make it relatable for couples"]
    }
  },

  /**
   * Build category-specific prompt section
   * @returns {string} - Formatted category-specific prompt
   */
  buildCategoryPrompt: function() {
    return `Category Guidelines for Couple Questions:

✅ MUST BE:
${this.guidelines.map(guideline => `- ${guideline}`).join('\n')}

Tone and Style:
- Tone: ${this.toneAndStyle.tone}
- Style: ${this.toneAndStyle.style}
- Mood: ${this.toneAndStyle.mood}
- Length: ${this.toneAndStyle.length}

Recommended Themes:
${this.themes.map(theme => `- ${theme}`).join('\n')}

❌ MUST NOT BE:
${this.avoidTopics.map(topic => `- ${topic}`).join('\n')}

Special Requirements:
${this.specialConsiderations.map(consideration => `- ${consideration}`).join('\n')}

`;
  },

  /**
   * Get example content for context
   * @returns {Array} - Array of example content
   */
  getExamples: function() {
    return this.examples;
  },

  /**
   * Validate if content fits this category
   * @param {Object} content - Content to validate
   * @returns {boolean} - Whether content is appropriate for this category
   */
  validateContent: function(content) {
    if (!content || !content.text_en) return false;

    const text = content.text_en.toLowerCase();

    // Check for avoided topics
    const hasAvoidedTopics = this.avoidTopics.some(topic => {
      const keywords = topic.toLowerCase().split(' ');
      return keywords.some(keyword => text.includes(keyword));
    });

    if (hasAvoidedTopics) return false;

    // Check for couple/romantic themes
    const coupleKeywords = ['romantic', 'partner', 'relationship', 'couple', 'date', 'love', 'romance', 'together', 'affection'];
    const hasCoupleElement = coupleKeywords.some(keyword => text.includes(keyword));

    // Check length (should be substantial for couple questions)
    const wordCount = content.text_en.split(' ').length;
    if (wordCount < 8 || wordCount > 25) return false;

    return hasCoupleElement;
  }
};

module.exports = coupleQuestions;
