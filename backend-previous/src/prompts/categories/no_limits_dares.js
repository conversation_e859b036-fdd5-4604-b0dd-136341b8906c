/**
 * No Limits Dares Prompt Configuration
 *
 * SUPER VULGAR dares with dirty and daring Dominican words.
 * One sentence max, no explanations.
 */

const noLimitsDares = {
  /**
   * Category metadata
   */
  metadata: {
    category: "no_limits_dares",
    gameMode: "dares",
    difficulty: "no_limits",
    description: "No Limits Dares - Super vulgar, Dominican sexual dares"
  },

  /**
   * Specific guidelines for no limits dares
   */
  guidelines: [
    "Address the player directly with commands (Haz..., Deja que..., Muestra..., etc.)",
    "Include group involvement dares (Elige a alguien del grupo para..., Haz que alguien coro...)",
    "Use bold and daring language with Dominican expressions",
    "One sentence maximum, no explanations or embellishments",
    "Direct, short, provocative and Dominican. Like coming from a mind that lives messing around",
    "Can involve daring physical actions, touching, and provocative behavior",
    "Sometimes include clarifications like: 'Si eres mujer, dale a skip' if dare is for men or vice versa",
    "Language should be Dominican, loaded with double meaning",
    "Mix solo extreme dares with group interaction dares",
    "Focus on daring physical actions, touching, and provocative dares",
    "This is the most extreme category - bold and daring but within content guidelines"
  ],

  /**
   * Tone and style requirements
   */
  toneAndStyle: {
    tone: "Super vulgar, direct, morbid, malicious, horny",
    style: "100% Dominican with extreme sexual slang",
    mood: "Extremely provocative, sexual, pressure-inducing",
    complexity: "Simple but loaded with vulgar meaning",
    length: "One sentence maximum"
  },

  /**
   * Content themes and topics
   */
  themes: [
    "Extreme sexual actions and touching",
    "Provocative physical dares with nudity",
    "Sexual performances and demonstrations",
    "Intimate touching and contact",
    "Sexual positions and movements",
    "Provocative undressing and exposure",
    "Sexual phone calls and messages",
    "Intimate physical challenges",
    "Sexual simulations and demonstrations",
    "Extreme sexual communication",
    "Sexual secrets and confessions",
    "Vulgar sexual interactions"
  ],

  /**
   * Content examples for inspiration (DO NOT COPY THESE - they are for reference only)
   */
  examples: [
    {
      text_en: "Call someone and tell them you wanna f*ck them right now.",
      text_es: "Llama a alguien y dile que tienes ganas de cogértelo/a ahora mismo.",
      text_dom: "Llama a alguien y dile que tienes ganas de singártelo/a ahora mismo."
    },
    {
      text_en: "Lick someone's dick/pussy over their clothes.",
      text_es: "Lame el pito/cuca a alguien del grupo por encima de la ropa.",
      text_dom: "Lámbele el gueb/toto a alguien del grupo por encima de la ropa."
    },
    {
      text_en: "Get on all fours or pick someone from the group and pretend you're fucking.",
      text_es: "Ponte de perrito o pon a alguien del grupo y simulen que están cogiendo.",
      text_dom: "Ponte en 4 o pon a alguien del grupo y simulen que están singando."
    },
    {
      text_en: "Let someone lift your shirt and kiss one of your nipples.",
      text_es: "Deja que alguien te suba la camisa/blusa y te dé un beso en un pezón.",
      text_dom: "Deja que alguien te suba la camisa/blusa y te dé un beso en un pezón."
    },
    {
      text_en: "Show someone in the group one of your saved nudes, but just for 5 seconds.",
      text_es: "Enséñale a alguien del grupo una nude que tengas, pero solo por 5 segundos.",
      text_dom: "Enséñale a alguien del grupo una nude que tengas, pero solo por 5 segundos."
    }
  ],

  /**
   * Things to avoid for this category
   */
  avoidTopics: [
    "Illegal activities or content",
    "Non-consensual activities",
    "Actions that could cause serious physical harm",
    "Dares involving minors or inappropriate age content",
    "Extreme violence or dangerous activities",
    "Self-harm or dangerous stunts",
    "Actions that could cause permanent damage",
    "Dares that could result in arrest",
    "Activities that violate platform policies",
    "Content that could cause severe psychological harm"
  ],

  /**
   * Special considerations for no limits dares
   */
  specialConsiderations: [
    "All activities must still be legal and consensual",
    "Ensure participants can safely decline any dare",
    "Consider the long-term consequences of actions",
    "Maintain respect for human dignity even in extreme dares",
    "Ensure dares don't permanently damage relationships",
    "Consider the environment and setting appropriateness",
    "Balance extreme excitement with basic safety",
    "Ensure all participants are comfortable with the intensity level"
  ],

  /**
   * Safety and consent guidelines
   */
  safetyGuidelines: [
    "All activities must be consensual",
    "No actions that could cause serious physical injury",
    "Respect absolute boundaries and limits",
    "Ensure safe words or opt-out options are available",
    "No dares that could cause lasting psychological harm",
    "Consider the safety of all participants and bystanders",
    "Maintain basic respect for human dignity",
    "Ensure activities are legal in the current jurisdiction"
  ],

  /**
   * Language-specific guidelines
   */
  languageSpecific: {
    english: {
      style: "Use bold, direct language",
      tone: "Uninhibited but respectful",
      considerations: ["Can use explicit vocabulary", "Maintain sophistication", "Be direct and clear"]
    },
    spanish: {
      style: "Use passionate, intense Spanish",
      tone: "Bold and expressive",
      considerations: ["Include passionate expressions", "Use direct language", "Maintain intensity"]
    },
    dominican: {
      style: "Use natural, bold Dominican expressions",
      tone: "Direct and passionate",
      considerations: ["Include local bold expressions", "Keep it real and direct", "Use familiar, intimate tone"]
    }
  },

  /**
   * Build category-specific prompt section
   * @returns {string} - Formatted category-specific prompt
   */
  buildCategoryPrompt: function() {
    return `Category Guidelines for No Limits Dares:
${this.guidelines.map(guideline => `- ${guideline}`).join('\n')}

Tone and Style:
- Tone: ${this.toneAndStyle.tone}
- Style: ${this.toneAndStyle.style}
- Mood: ${this.toneAndStyle.mood}
- Complexity: ${this.toneAndStyle.complexity}
- Length: ${this.toneAndStyle.length}

Recommended Themes:
${this.themes.map(theme => `- ${theme}`).join('\n')}

Topics to Avoid:
${this.avoidTopics.map(topic => `- ${topic}`).join('\n')}

Safety and Consent Guidelines:
${this.safetyGuidelines.map(guideline => `- ${guideline}`).join('\n')}

Special Considerations:
${this.specialConsiderations.map(consideration => `- ${consideration}`).join('\n')}

IMPORTANT: While this is the "No Limits" category, all content must still be legal, ethical, and consensual. Push boundaries creatively while maintaining respect for human dignity and safety.

`;
  },

  /**
   * Get example content for context
   * @returns {Array} - Array of example content
   */
  getExamples: function() {
    return this.examples;
  },

  /**
   * Validate if content fits this category
   * @param {Object} content - Content to validate
   * @returns {boolean} - Whether content is appropriate for this category
   */
  validateContent: function(content) {
    if (!content || !content.text_en) return false;

    const text = content.text_en.toLowerCase();

    // Check for avoided topics (illegal/harmful content)
    const hasAvoidedTopics = this.avoidTopics.some(topic => {
      const keywords = topic.toLowerCase().split(' ');
      return keywords.some(keyword => text.includes(keyword));
    });

    if (hasAvoidedTopics) return false;

    // Check for bold/extreme themes
    const boldKeywords = ['strip', 'naked', 'kiss', 'make out', 'confess', 'secret', 'wildest', 'extreme', 'dare', 'bold'];
    const hasBoldElement = boldKeywords.some(keyword => text.includes(keyword));

    // Check for action words
    const actionWords = ['strip', 'make', 'call', 'let', 'confess', 'do', 'perform', 'show', 'tell'];
    const hasActionWord = actionWords.some(word => text.includes(word));

    // Check length (should be substantial for no limits dares)
    const wordCount = content.text_en.split(' ').length;
    if (wordCount < 10 || wordCount > 35) return false;

    return hasBoldElement && hasActionWord;
  }
};

module.exports = noLimitsDares;
