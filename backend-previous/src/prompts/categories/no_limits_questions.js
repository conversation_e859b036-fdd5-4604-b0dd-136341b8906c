/**
 * No Limits Questions Prompt Configuration
 *
 * SUPER VULGAR questions with dirty and daring Dominican words.
 * Third person, one sentence max, no explanations.
 */

const noLimitsQuestions = {
  /**
   * Category metadata
   */
  metadata: {
    category: "no_limits_questions",
    gameMode: "questions",
    difficulty: "no_limits",
    description: "No Limits Questions - Super vulgar, third person, Dominican sexual questions"
  },

  /**
   * Specific guidelines for no limits questions
   */
  guidelines: [
    "MUST be in third person directed at the player (¿Cuál es..., ¿Qué has..., ¿Te han...)",
    "Include group involvement questions (¿Quién del grupo crees que...?, ¿Quién coro crees que...?)",
    "Include extreme hypothetical questions (<PERSON> tuvieras..., <PERSON> pudieras..., <PERSON> fueras..., <PERSON> te ofrecieran..., Si estuvieras...)",
    "Use bold and daring language with Dominican expressions",
    "One sentence maximum, no explanations or embellishments",
    "Use varied question starters: ¿Cuál es, ¿<PERSON><PERSON> has, ¿Te han, ¿<PERSON><PERSON>én del grupo, ¿<PERSON>uién coro, <PERSON> t<PERSON>vier<PERSON>, <PERSON> pu<PERSON>, <PERSON> estuvier<PERSON>",
    "Direct, short, provocative and Dominican. Like coming from a mind that lives messing around",
    "Make the person feel pressure or curiosity when answering",
    "Sometimes include clarifications like: 'Si eres mujer, dale a skip' if question is for men or vice versa",
    "Language should be Dominican, loaded with double meaning",
    "Mix personal provocative questions with group dynamics questions and extreme hypothetical scenarios",
    "Hypothetical questions should push boundaries and create intense curiosity",
    "This is the most extreme category - bold and daring but not explicitly vulgar"
  ],

  /**
   * Tone and style requirements
   */
  toneAndStyle: {
    tone: "Super vulgar, direct, morbid, malicious, horny",
    style: "100% Dominican street language, loaded with sexual double meaning",
    mood: "Extremely provocative, pressure-inducing, morbid curiosity",
    complexity: "Simple but loaded with vulgar meaning",
    length: "One sentence maximum"
  },

  /**
   * Content themes and topics
   */
  themes: [
    "Extreme sexual experiences and techniques",
    "Vulgar sexual preferences and positions",
    "Dirty sexual encounters and stories",
    "Sexual fantasies and perversions",
    "Sexual performance and extreme skills",
    "Sexual body parts and vulgar functions",
    "Sexual situations and extreme locations",
    "Sexual partners and dirty relationships",
    "Sexual secrets and vulgar confessions",
    "Sexual comparisons and extreme preferences",
    "Sexual adventures and perverse experiments",
    "Sexual mistakes and dirty regrets"
  ],

  /**
   * Content examples for inspiration (DO NOT COPY THESE - they are for reference only)
   */
  examples: [
    {
      text_en: "What do you like more, cum in your mouth or cum in your pussy?",
      text_es: "¿Dónde prefieres la leche: en la boca o en la cuca?",
      text_dom: "¿Dónde prefieres la leche: en la boca o en el toto?"
    },
    {
      text_en: "Who the hell taught you to suck like that?",
      text_es: "¿Quién fue el/la maldito/a que te enseñó a hacer sexo oral así?",
      text_dom: "¿Quién fue el/la maldito/a que te enseñó a mamar así?"
    },
    {
      text_en: "Have you been fucked so good you didn't even know how many times you came?",
      text_es: "¿Te han cogido tan rico que no supiste cuántas veces te viniste?",
      text_dom: "¿Te han singado tan rico que ni tú supiste cuántas veces te viniste?"
    },
    {
      text_en: "What's the craziest thing you did just to get some cum that night?",
      text_es: "¿Qué fue lo más loco que hiciste para que te dieran leche esa noche?",
      text_dom: "¿Qué fue lo más loco que hiciste pa' que te dieran leche esa noche?"
    },
    {
      text_en: "You like to get fucked with love or with hate?",
      text_es: "¿A ti te gusta que te cojan con rabia o con amor?",
      text_dom: "¿A ti te gusta que te singuen con odio o con cariño?"
    },
    {
      text_en: "Ever came inside and played dumb about it? (If you're a girl, skip)",
      text_es: "¿Alguna vez te viniste adentro e hiciste como que no pasó nada? Si eres mujer, dale a Saltar.",
      text_dom: "¿Alguna vez te viniste adentro y te hiciste el loco? Si eres mujer, dale a Saltar."
    },
    {
      text_en: "If you had to choose someone from the group to fuck your ex in front of you, who would you pick?",
      text_es: "Si tuvieras que elegir a alguien del grupo para que se coja a tu ex enfrente tuyo, ¿a quién elegirías?",
      text_dom: "Si tuvieras que elegir a alguien coro pa' que se singue a tu ex enfrente tuyo, ¿a quién elegirías?"
    },
    {
      text_en: "If you could have sex with any family member of someone in this group, who would it be?",
      text_es: "Si pudieras tener sexo con cualquier familiar de alguien de este grupo, ¿quién sería?",
      text_dom: "Si pudieras singar con cualquier familiar de alguien coro, ¿quién sería?"
    },
    {
      text_en: "If you were forced to choose between being a porn star or never having sex again, what would you pick?",
      text_es: "Si te obligaran a elegir entre ser estrella porno o nunca más tener sexo, ¿qué elegirías?",
      text_dom: "Si te obligaran a elegir entre ser estrella porno o nunca más singar, ¿qué elegirías?"
    }
  ],

  /**
   * Things to avoid for this category
   */
  avoidTopics: [
    "Too many questions starting with '¿Tú...' - use varied starters",
    "Conversations between two people (nothing like '¿tú también...?', '¿soy la única que...?')",
    "Poetic, deep, or soft emotional questions. This is not reflection, it's morbid curiosity",
    "Very long or embellished phrases",
    "Soft questions - this is NO LIMITS, make it SUPER VULGAR",
    "Non-consensual activities",
    "Illegal sexual activities",
    "Underage references",
    "Extreme violence or abuse"
  ],

  /**
   * Special considerations for no limits questions
   */
  specialConsiderations: [
    "Questions should be in third person and apply to both men and women",
    "Use dirty and daring Dominican words like singar, ñema, toto, mamao, culo, pilonear",
    "One sentence maximum, no explanations or embellishments",
    "Use varied question starters: ¿Cuál fue, ¿Qué fue, ¿Qué es, ¿Te han, ¿Quién fue, ¿Y si",
    "Direct, short, morbid and Dominican. Like coming from a mind that lives messing around",
    "Make the person feel pressure or morbid curiosity when answering",
    "Sometimes include clarifications like: 'Si eres mujer, dale a skip' if question is for men or vice versa",
    "Language should be 100% Dominican, loaded with double meaning",
    "SUPER VULGAR - this is the most extreme category, no limits on vulgarity"
  ],

  /**
   * Language-specific guidelines
   */
  languageSpecific: {
    english: {
      style: "Direct, casual but understandable for B2 English users",
      tone: "Super vulgar, bold, sexual, provocative",
      considerations: ["Use explicit sexual slang", "Keep it direct and vulgar", "Make it extremely provocative"]
    },
    spanish: {
      style: "Standard Spanish with explicit sexual terms",
      tone: "Direct, vulgar and sexual",
      considerations: ["Use explicit sexual language", "Keep it direct and vulgar", "Make it extremely morbid"]
    },
    dominican: {
      style: "100% Dominican with extreme sexual slang",
      tone: "Super vulgar, morbid, malicious, horny",
      considerations: ["Heavy use of singar, ñema, toto, guevo, mamao, culo, pilonear", "Load with extreme double meaning", "Make it street-level Dominican and super vulgar"]
    }
  },

  /**
   * Build category-specific prompt section
   * @returns {string} - Formatted category-specific prompt
   */
  buildCategoryPrompt: function() {
    return `Category Guidelines for NO LIMITS Questions - SUPER VULGAR:

✅ MUST BE:
${this.guidelines.map(guideline => `- ${guideline}`).join('\n')}

Tone and Style:
- Tone: ${this.toneAndStyle.tone}
- Style: ${this.toneAndStyle.style}
- Mood: ${this.toneAndStyle.mood}
- Length: ${this.toneAndStyle.length}

Recommended Themes:
${this.themes.map(theme => `- ${theme}`).join('\n')}

❌ MUST NOT BE:
${this.avoidTopics.map(topic => `- ${topic}`).join('\n')}

Special Requirements:
${this.specialConsiderations.map(consideration => `- ${consideration}`).join('\n')}

IMPORTANT: This is NO LIMITS - make it SUPER VULGAR with extreme Dominican sexual slang. No holding back on vulgarity.

`;
  },

  /**
   * Get example content for context
   * @returns {Array} - Array of example content
   */
  getExamples: function() {
    return this.examples;
  },

  /**
   * Validate if content fits this category
   * @param {Object} content - Content to validate
   * @returns {boolean} - Whether content is appropriate for this category
   */
  validateContent: function(content) {
    if (!content || !content.text_en) return false;

    const text = content.text_en.toLowerCase();

    // Check for avoided topics (illegal/harmful content)
    const hasAvoidedTopics = this.avoidTopics.some(topic => {
      const keywords = topic.toLowerCase().split(' ');
      return keywords.some(keyword => text.includes(keyword));
    });

    if (hasAvoidedTopics) return false;

    // Check for bold/adventurous themes
    const boldKeywords = ['secret', 'fantasy', 'taboo', 'wildest', 'deepest', 'never', 'forbidden', 'extreme', 'bold', 'daring'];
    const hasBoldElement = boldKeywords.some(keyword => text.includes(keyword));

    // Check length (should be substantial for no limits questions)
    const wordCount = content.text_en.split(' ').length;
    if (wordCount < 10 || wordCount > 35) return false;

    return hasBoldElement;
  }
};

module.exports = noLimitsQuestions;
