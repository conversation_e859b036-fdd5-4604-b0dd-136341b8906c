/**
 * Spicy Questions Prompt Configuration
 *
 * Prompts and guidelines specifically for generating spicy questions
 * Direct, short, morbid and Dominican. Like coming from a mind that lives messing around.
 */

const spicyQuestions = {
  /**
   * Category metadata
   */
  metadata: {
    category: "spicy_questions",
    gameMode: "questions",
    difficulty: "spicy",
    description: "Spicy Questions - Direct, morbid, Dominican questions with sexual content"
  },

  /**
   * Specific guidelines for spicy questions
   */
  guidelines: [
    "MUST be in third person directed at the player (¿Cuál es..., ¿Qué has..., ¿Te han...)",
    "Include group involvement questions (¿Quién del grupo crees que...?, ¿Quién coro crees que...?)",
    "Include hypothetical sexual questions (<PERSON> tuvieras..., <PERSON> pudier<PERSON>..., <PERSON> fueras..., <PERSON> te ofrecieran..., Si estuvieras...)",
    "Use dirty and daring words like singar, ñema, toto, mamao, culo, pilonear, etc.",
    "One sentence maximum, no explanations or embellishments",
    "Use varied question starters: ¿Cuál es, ¿<PERSON><PERSON> has, ¿<PERSON> han, ¿Quién del grupo, ¿<PERSON><PERSON>én coro, <PERSON>, <PERSON> pu<PERSON>, <PERSON> estuvier<PERSON>",
    "Direct, short, morbid and Dominican. Like coming from a mind that lives messing around",
    "Make the person feel pressure or morbid curiosity when answering",
    "Sometimes include clarifications like: 'Si eres mujer, dale a skip' if question is for men or vice versa",
    "Inspired by Dominican tone, loaded with double meaning",
    "Mix personal sexual questions with group dynamics questions and sexual hypothetical scenarios",
    "Hypothetical questions should create sexual tension and morbid curiosity"
  ],

  /**
   * Tone and style requirements
   */
  toneAndStyle: {
    tone: "Direct, morbid, malicious, horny",
    style: "Dominican street language, loaded with sexual double meaning",
    mood: "Provocative, pressure-inducing, morbid curiosity",
    complexity: "Simple but loaded with meaning",
    length: "One sentence maximum"
  },

  /**
   * Content themes and topics
   */
  themes: [
    "Sexual experiences and techniques",
    "Sexual preferences and positions",
    "Past sexual encounters",
    "Sexual fantasies and desires",
    "Sexual performance and skills",
    "Sexual body parts and functions",
    "Sexual situations and locations",
    "Sexual partners and relationships",
    "Sexual secrets and confessions",
    "Sexual comparisons and preferences",
    "Sexual adventures and experiments",
    "Sexual mistakes and regrets"
  ],

  /**
   * Content examples for inspiration (DO NOT COPY THESE - they are for reference only)
   */
  examples: [
    {
      text_en: "What's the most fucked up place you've ever had sex?",
      text_es: "¿Cuál ha sido el sitio más atrevido en donde has cogido?",
      text_dom: "¿Cuál ha sido el sitio más perro en donde has singao?"
    },
    {
      text_en: "Can you feel the difference between one pussy/dick and another?",
      text_es: "¿Tú sientes la diferencia entre una cuca/pito y otro?",
      text_dom: "¿Tú sientes la diferencia entre un toto/guevo y otro?"
    },
    {
      text_en: "Who's someone you had sex with but you never expected to?",
      text_es: "¿Con quién cogiste que nunca pensaste que pasaría?",
      text_dom: "¿Con quién tú singaste que nunca pensaste que pasaría?"
    },
    {
      text_en: "What's your top tip when eating pussy?",
      text_es: "¿Cuál es tu mejor truco para mamar cuca?",
      text_dom: "¿Cuál es tu mejor truco pa' mamar toto?"
    },
    {
      text_en: "What do you think about while receiving/giving it doggystyle?",
      text_es: "¿En qué piensas cuando le das o te dan en perrito?",
      text_dom: "¿En qué tú piensas cuando le das o te dan en cuatro?"
    },
    {
      text_en: "What is the most random way you ended up getting laid?",
      text_es: "¿Cuál es la manera más inesperada en la que terminaste cogiendo?",
      text_dom: "¿Cuál ha sido la forma más random en que terminaste singando?"
    },
    {
      text_en: "If you had to choose someone from the group to have a threesome with, who would complete the trio?",
      text_es: "Si tuvieras que elegir a alguien del grupo para un trío, ¿quién completaría el trío?",
      text_dom: "Si tuvieras que elegir a alguien coro pa' un trío, ¿quién completaría el trío?"
    },
    {
      text_en: "If you could have sex in any place in the world without consequences, where would it be?",
      text_es: "Si pudieras tener sexo en cualquier lugar del mundo sin consecuencias, ¿dónde sería?",
      text_dom: "Si pudieras singar en cualquier lugar del mundo sin consecuencias, ¿dónde sería?"
    },
    {
      text_en: "If you were forced to choose between never having oral sex again or never having penetration again, what would you pick?",
      text_es: "Si te obligaran a elegir entre nunca más tener sexo oral o nunca más penetración, ¿qué elegirías?",
      text_dom: "Si te obligaran a elegir entre nunca más mamar o nunca más que te metan, ¿qué elegirías?"
    }
  ],

  /**
   * Things to avoid for this category
   */
  avoidTopics: [
    "Too many questions starting with '¿Tú...' - use varied starters",
    "Conversations between two people (nothing like '¿tú también...?', '¿soy la única que...?')",
    "Poetic, deep, or soft emotional questions. This is not reflection, it's morbid curiosity",
    "Very long or embellished phrases",
    "Soft questions like '¿te gusta hacerlo en lugares raros?' - that's generic and bland",
    "Non-consensual activities",
    "Illegal sexual activities",
    "Underage references",
    "Harmful or dangerous activities"
  ],

  /**
   * Special considerations for spicy questions
   */
  specialConsiderations: [
    "Questions should be in third person and apply to both men and women",
    "Use dirty and daring Dominican words like singar, ñema, toto, mamao, culo, pilonear",
    "One sentence maximum, no explanations or embellishments",
    "Use varied question starters: ¿Cuál fue, ¿Qué fue, ¿Qué es, ¿Te han, ¿Quién fue, ¿Y si",
    "Direct, short, morbid and Dominican. Like coming from a mind that lives messing around",
    "Make the person feel pressure or morbid curiosity when answering",
    "Sometimes include clarifications like: 'Si eres mujer, dale a skip' if question is for men or vice versa",
    "Language should be 100% Dominican, loaded with double meaning"
  ],

  /**
   * Language-specific guidelines
   */
  languageSpecific: {
    english: {
      style: "Direct, casual but understandable for B2 English users",
      tone: "Bold, sexual, provocative",
      considerations: ["Use sexual slang", "Keep it direct", "Make it provocative"]
    },
    spanish: {
      style: "Standard Spanish with sexual terms",
      tone: "Direct and sexual",
      considerations: ["Use sexual language", "Keep it direct", "Make it morbid"]
    },
    dominican: {
      style: "100% Dominican with heavy sexual slang",
      tone: "Morbid, malicious, horny",
      considerations: ["Use singar, ñema, toto, guevo, mamao, culo, pilonear", "Load with double meaning", "Make it street-level Dominican"]
    }
  },

  /**
   * Build category-specific prompt section
   * @returns {string} - Formatted category-specific prompt
   */
  buildCategoryPrompt: function() {
    return `Category Guidelines for Spicy Questions:

✅ MUST BE:
${this.guidelines.map(guideline => `- ${guideline}`).join('\n')}

Tone and Style:
- Tone: ${this.toneAndStyle.tone}
- Style: ${this.toneAndStyle.style}
- Mood: ${this.toneAndStyle.mood}
- Length: ${this.toneAndStyle.length}

Recommended Themes:
${this.themes.map(theme => `- ${theme}`).join('\n')}

❌ MUST NOT BE:
${this.avoidTopics.map(topic => `- ${topic}`).join('\n')}

Special Requirements:
${this.specialConsiderations.map(consideration => `- ${consideration}`).join('\n')}

`;
  },

  /**
   * Get example content for context
   * @returns {Array} - Array of example content
   */
  getExamples: function() {
    return this.examples;
  },

  /**
   * Validate if content fits this category
   * @param {Object} content - Content to validate
   * @returns {boolean} - Whether content is appropriate for this category
   */
  validateContent: function(content) {
    if (!content || !content.text_en) return false;

    const text = content.text_en.toLowerCase();

    // Check for avoided topics
    const hasAvoidedTopics = this.avoidTopics.some(topic => {
      const keywords = topic.toLowerCase().split(' ');
      return keywords.some(keyword => text.includes(keyword));
    });

    if (hasAvoidedTopics) return false;

    // Check for spicy themes
    const spicyKeywords = ['romantic', 'intimate', 'attraction', 'desire', 'fantasy', 'partner', 'relationship', 'love', 'passionate'];
    const hasSpicyElement = spicyKeywords.some(keyword => text.includes(keyword));

    // Check length (should be substantial for spicy questions)
    const wordCount = content.text_en.split(' ').length;
    if (wordCount < 8 || wordCount > 30) return false;

    return hasSpicyElement;
  }
};

module.exports = spicyQuestions;
