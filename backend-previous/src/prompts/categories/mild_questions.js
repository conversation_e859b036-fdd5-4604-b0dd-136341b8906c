/**
 * Mild Questions Prompt Configuration
 *
 * Bold, uncomfortable or challenging questions/dares that provoke laughter or tension,
 * without being sexual.
 */

const mildQuestions = {
  /**
   * Category metadata
   */
  metadata: {
    category: "mild_questions",
    gameMode: "questions",
    difficulty: "mild",
    description: "Mild Questions - Bold, uncomfortable, challenging questions that provoke tension"
  },

  /**
   * Specific guidelines for mild questions
   */
  guidelines: [
    "MUST be in third person directed at the player (¿Cuál es..., ¿Qué has..., ¿Te han...)",
    "Include group involvement questions (¿Quién del grupo crees que...?, ¿Quién coro crees que...?)",
    "Include hypothetical questions (<PERSON> tuvieras..., <PERSON> pudier<PERSON>..., <PERSON> fueras..., <PERSON> te ofrecieran..., Si estuvieras...)",
    "Moderate Dominican expressions when necessary",
    "One sentence maximum, no explanations or embellishments",
    "Use varied question starters: ¿Cuál es, ¿Qué has, ¿Te han, ¿<PERSON><PERSON>én del grupo, ¿<PERSON><PERSON>én coro, <PERSON> tuvier<PERSON>, <PERSON> pudier<PERSON>, <PERSON> estuvier<PERSON>",
    "Focus on concepts, experiences and human contradictions that are spicy but reflexive",
    "Mix personal questions with group dynamics questions and hypothetical scenarios",
    "Don't accuse the person directly - be more observational than confessional",
    "Don't base questions on specific people (not 'tu ex', 'tu amigo', etc.)",
    "Hypothetical questions should create uncomfortable but interesting dilemmas",
    "Make them feel like mental traps: you think you can answer easily... until you open your mouth",
    "Make the person feel some pressure when answering (if it's a challenging question)"
  ],

  /**
   * Tone and style requirements
   */
  toneAndStyle: {
    tone: "Bold, uncomfortable, challenging",
    style: "Provocative questions that cause laughter or tension without being sexual",
    mood: "Pressure-inducing but not sexual",
    complexity: "Simple but loaded with meaning",
    length: "One sentence maximum"
  },

  /**
   * Content themes and topics
   */
  themes: [
    "Uncomfortable personal experiences",
    "Challenging relationship situations",
    "Awkward social moments",
    "Personal contradictions and hypocrisies",
    "Embarrassing but not sexual situations",
    "Moral dilemmas and gray areas",
    "Social pressure situations",
    "Personal failures and mistakes",
    "Uncomfortable truths about behavior",
    "Situations that reveal character",
    "Moments of personal weakness",
    "Social awkwardness and tension"
  ],

  /**
   * Content examples for inspiration (DO NOT COPY THESE - they are for reference only)
   */
  examples: [
    {
      text_en: "Which of your friends is most likely to end up in jail?",
      text_es: "¿Cuál de tus amigos es más probable que termine preso?",
      text_dom: "¿Cuál de tus amigos es más probable que termine preso?"
    },
    {
      text_en: "Have you 'hooked up' with someone knowing they had a partner?",
      text_es: "¿Has cogido con alguien sabiendo que tenía pareja?",
      text_dom: "¿Has 'mangao' con alguien sabiendo que tenía jevo o jeva?"
    },
    {
      text_en: "What was the lamest excuse you gave to not admit you were cheating?",
      text_es: "¿Cuál fue la excusa más tonta que diste para no admitir que estabas montando los cuernos?",
      text_dom: "¿Cuál fue la excusa más bultera que diste pa' no decir que tabas pegando los cuernos?"
    },
    {
      text_en: "Which friend would you never do business with because you know they're full of it?",
      text_es: "¿Con qué amigo nunca harías negocios porque sabes que es puro cuento?",
      text_dom: "¿Con cuál amigo tú nunca harías negocio porque tú sabes que es bultero?"
    },
    {
      text_en: "What's the dirtiest thing you ever did that you don't regret at all?",
      text_es: "¿Qué fue lo más sucio que tú hiciste y no te arrepientes para nada?",
      text_dom: "¿Qué fue lo más rastrero que tú hiciste y no te arrepientes pa' ná?"
    },
    {
      text_en: "If you had to confess your biggest secret to someone in this group, who would you choose?",
      text_es: "Si tuvieras que confesar tu secreto más grande a alguien de este grupo, ¿a quién elegirías?",
      text_dom: "Si tuvieras que confesar tu secreto más grande a alguien coro, ¿a quién elegirías?"
    },
    {
      text_en: "If you could erase one embarrassing moment from your past, which would it be?",
      text_es: "Si pudieras borrar un momento vergonzoso de tu pasado, ¿cuál sería?",
      text_dom: "Si pudieras borrar un momento vergonzoso de tu pasado, ¿cuál sería?"
    },
    {
      text_en: "If you were forced to choose between your phone and your dignity for a week, what would you pick?",
      text_es: "Si te obligaran a elegir entre tu teléfono y tu dignidad por una semana, ¿qué elegirías?",
      text_dom: "Si te obligaran a elegir entre tu teléfono y tu dignidad por una semana, ¿qué elegirías?"
    }
  ],

  /**
   * Things to avoid for this category
   */
  avoidTopics: [
    "Highly intimate or sexual content",
    "Deeply traumatic experiences",
    "Controversial political opinions",
    "Religious debates",
    "Financial details",
    "Illegal activities",
    "Extremely embarrassing situations",
    "Family secrets or drama",
    "Mental health issues (serious)",
    "Addiction or substance abuse"
  ],

  /**
   * Special considerations for mild questions
   */
  specialConsiderations: [
    "Questions should be appropriate for workplace social events",
    "Can explore personal topics but maintain boundaries",
    "Should encourage sharing without making people uncomfortable",
    "Include questions that reveal personality without being invasive",
    "Balance personal depth with social appropriateness",
    "Consider cultural differences in what's considered 'mild'",
    "Questions should work well for people who don't know each other well"
  ],

  /**
   * Language-specific guidelines
   */
  languageSpecific: {
    english: {
      style: "Use engaging, thoughtful language",
      tone: "Curious but respectful",
      considerations: ["Can use more complex vocabulary", "Include nuanced questions"]
    },
    spanish: {
      style: "Use expressive Spanish with some regional flexibility",
      tone: "Warm and interested",
      considerations: ["Can include some regional expressions", "Use both formal and informal registers"]
    },
    dominican: {
      style: "Use natural Dominican expressions",
      tone: "Friendly and curious",
      considerations: ["Include local expressions", "Use familiar 'tú' form", "Keep it conversational"]
    }
  },

  /**
   * Build category-specific prompt section
   * @returns {string} - Formatted category-specific prompt
   */
  buildCategoryPrompt: function() {
    return `Category Guidelines for Mild Questions:
${this.guidelines.map(guideline => `- ${guideline}`).join('\n')}

Tone and Style:
- Tone: ${this.toneAndStyle.tone}
- Style: ${this.toneAndStyle.style}
- Mood: ${this.toneAndStyle.mood}
- Complexity: ${this.toneAndStyle.complexity}
- Length: ${this.toneAndStyle.length}

Recommended Themes:
${this.themes.map(theme => `- ${theme}`).join('\n')}

Topics to Avoid:
${this.avoidTopics.map(topic => `- ${topic}`).join('\n')}

Special Considerations:
${this.specialConsiderations.map(consideration => `- ${consideration}`).join('\n')}

`;
  },

  /**
   * Get example content for context
   * @returns {Array} - Array of example content
   */
  getExamples: function() {
    return this.examples;
  },

  /**
   * Validate if content fits this category
   * @param {Object} content - Content to validate
   * @returns {boolean} - Whether content is appropriate for this category
   */
  validateContent: function(content) {
    if (!content || !content.text_en) return false;

    const text = content.text_en.toLowerCase();

    // Check for avoided topics
    const hasAvoidedTopics = this.avoidTopics.some(topic => {
      const keywords = topic.toLowerCase().split(' ');
      return keywords.some(keyword => text.includes(keyword));
    });

    if (hasAvoidedTopics) return false;

    // Check length (should be reasonable for mild questions)
    const wordCount = content.text_en.split(' ').length;
    if (wordCount > 25) return false;

    // Should be more personal than casual but not too intimate
    const personalKeywords = ['you', 'your', 'yourself', 'personal', 'feel', 'think', 'believe'];
    const hasPersonalElement = personalKeywords.some(keyword => text.includes(keyword));

    return hasPersonalElement;
  }
};

module.exports = mildQuestions;
