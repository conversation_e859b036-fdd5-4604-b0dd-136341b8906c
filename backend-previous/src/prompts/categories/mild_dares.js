/**
 * Mild Dares Prompt Configuration
 *
 * Bold, uncomfortable or challenging dares that provoke laughter or tension,
 * without being sexual.
 */

const mildDares = {
  /**
   * Category metadata
   */
  metadata: {
    category: "mild_dares",
    gameMode: "dares",
    difficulty: "mild",
    description: "Mild Dares - Bold, uncomfortable, challenging dares that provoke tension"
  },

  /**
   * Specific guidelines for mild dares
   */
  guidelines: [
    "Address the player directly with commands (Haz..., Llama..., Envía..., etc.)",
    "Include group involvement dares (Elige a alguien del grupo para..., Haz que alguien coro...)",
    "Moderate Dominican expressions when necessary",
    "One sentence maximum, clear and direct instructions",
    "Focus on actions that are entertaining but create some pressure",
    "Avoid dares involving objects, talking to objects, or acting with inanimate things",
    "Avoid dares about telling someone in the group something - those are stupid",
    "No dares like 'apologize to the air as if you cheated on WiFi' - that makes no sense",
    "Focus on physical actions, creative expressions, or social interactions that make sense",
    "Mix solo dares with group interaction dares",
    "Make the person feel some pressure when performing (if it's a challenging dare)",
    "Can involve mild embarrassment but nothing humiliating or sexual"
  ],

  /**
   * Tone and style requirements
   */
  toneAndStyle: {
    tone: "Encouraging and slightly challenging",
    style: "Clear instructions with some complexity",
    mood: "Adventurous but supportive",
    complexity: "Moderate complexity",
    length: "Medium length (8-20 words)"
  },

  /**
   * Content themes and topics
   */
  themes: [
    "Uncomfortable social interactions",
    "Challenging physical actions",
    "Embarrassing but not sexual situations",
    "Social pressure situations",
    "Phone calls and messages that create tension",
    "Public actions that cause mild embarrassment",
    "Clothing swaps and outfit changes",
    "Food challenges and eating dares",
    "Social media challenges",
    "Stranger interactions",
    "Physical discomfort challenges",
    "Performance dares that create pressure"
  ],

  /**
   * Content examples for inspiration (DO NOT COPY THESE - they are for reference only)
   */
  examples: [
    {
      text_en: "Go to the supermarket, buy a can of powdered milk and eat 5 spoonfuls.",
      text_es: "Ve al supermercado, compra una lata de leche en polvo y cómete 5 cucharadas.",
      text_dom: "Ve al supermercado, compra una lata de leche en polvo y cómete 5 cucharadas."
    },
    {
      text_en: "Call a public place and moan for 5 seconds.",
      text_es: "Llama a un lugar público y gime durante 5 segundos.",
      text_dom: "Llama a un lugar público y gime diciendo: 'ay papi, que rico', por 5 segundos."
    },
    {
      text_en: "Swap pants with someone in the group without going to the bathroom.",
      text_es: "Cámbiate los pantalones con alguien del grupo sin irte al baño.",
      text_dom: "Cámbiate los pantalones con alguien del grupo sin irte al baño."
    },
    {
      text_en: "Go up to someone you don't know and ask for their phone number.",
      text_es: "Acércate a alguien que no conozcas y pídele su número de teléfono.",
      text_dom: "Acércate a alguien que no conozcas y pídele su número de teléfono."
    },
    {
      text_en: "Text your ex: I miss you bb.",
      text_es: "Escríbele a tu ex: Te extraño bb.",
      text_dom: "Escríbele a tu ex: Te extraño bb."
    }
  ],

  /**
   * Things to avoid for this category
   */
  avoidTopics: [
    "Anything that could cause serious embarrassment",
    "Actions that could damage relationships",
    "Dares involving strangers inappropriately",
    "Actions that could get someone in trouble",
    "Anything involving personal secrets",
    "Dares that could cause financial loss",
    "Actions that violate privacy",
    "Anything that could be considered harassment",
    "Dares involving illegal activities",
    "Actions that could cause lasting consequences"
  ],

  /**
   * Special considerations for mild dares
   */
  specialConsiderations: [
    "Dares should be appropriate for workplace social events",
    "Consider the social dynamics of the group",
    "Ensure dares don't cross personal boundaries",
    "Include escape clauses for uncomfortable situations",
    "Balance challenge with safety",
    "Consider cultural differences in what's acceptable",
    "Make sure dares are reversible or temporary",
    "Ensure dares don't exclude anyone from the group"
  ],

  /**
   * Safety guidelines
   */
  safetyGuidelines: [
    "No actions that could cause physical harm",
    "No dares that could damage reputation seriously",
    "No actions involving dangerous locations",
    "No dares that could violate laws or rules",
    "No actions that could cause emotional distress",
    "Always consider consent and comfort levels",
    "Ensure dares can be stopped if needed"
  ],

  /**
   * Language-specific guidelines
   */
  languageSpecific: {
    english: {
      style: "Use encouraging, adventurous language",
      tone: "Supportive but challenging",
      considerations: ["Include motivational elements", "Make it sound exciting", "Use clear action words"]
    },
    spanish: {
      style: "Use expressive, encouraging Spanish",
      tone: "Warm but challenging",
      considerations: ["Include enthusiastic language", "Use motivational phrases", "Keep it engaging"]
    },
    dominican: {
      style: "Use natural Dominican expressions with encouragement",
      tone: "Supportive and fun",
      considerations: ["Include local encouraging expressions", "Keep it motivational", "Use familiar tone"]
    }
  },

  /**
   * Build category-specific prompt section
   * @returns {string} - Formatted category-specific prompt
   */
  buildCategoryPrompt: function() {
    return `Category Guidelines for Mild Dares:
${this.guidelines.map(guideline => `- ${guideline}`).join('\n')}

Tone and Style:
- Tone: ${this.toneAndStyle.tone}
- Style: ${this.toneAndStyle.style}
- Mood: ${this.toneAndStyle.mood}
- Complexity: ${this.toneAndStyle.complexity}
- Length: ${this.toneAndStyle.length}

Recommended Themes:
${this.themes.map(theme => `- ${theme}`).join('\n')}

Topics to Avoid:
${this.avoidTopics.map(topic => `- ${topic}`).join('\n')}

Safety Guidelines:
${this.safetyGuidelines.map(guideline => `- ${guideline}`).join('\n')}

Special Considerations:
${this.specialConsiderations.map(consideration => `- ${consideration}`).join('\n')}

`;
  },

  /**
   * Get example content for context
   * @returns {Array} - Array of example content
   */
  getExamples: function() {
    return this.examples;
  },

  /**
   * Validate if content fits this category
   * @param {Object} content - Content to validate
   * @returns {boolean} - Whether content is appropriate for this category
   */
  validateContent: function(content) {
    if (!content || !content.text_en) return false;

    const text = content.text_en.toLowerCase();

    // Check for avoided topics
    const hasAvoidedTopics = this.avoidTopics.some(topic => {
      const keywords = topic.toLowerCase().split(' ');
      return keywords.some(keyword => text.includes(keyword));
    });

    if (hasAvoidedTopics) return false;

    // Check for action words and social elements
    const actionWords = ['call', 'text', 'post', 'speak', 'do', 'perform', 'sing', 'dance', 'tell', 'ask'];
    const socialWords = ['someone', 'contact', 'friend', 'group', 'everyone', 'others'];

    const hasActionWord = actionWords.some(word => text.includes(word));
    const hasSocialElement = socialWords.some(word => text.includes(word)) || text.includes('for') || text.includes('to');

    // Check length (should be substantial for mild dares)
    const wordCount = content.text_en.split(' ').length;
    if (wordCount < 6 || wordCount > 25) return false;

    return hasActionWord && (hasSocialElement || text.includes('minute') || text.includes('time'));
  }
};

module.exports = mildDares;
