# TapTrap Backend API

Backend API for TapTrap game that replaces Airtable.

## Features

- Game content API
- Admin dashboard for content management
- JWT authentication for admin users
- MongoDB database storage
- Response encryption for sensitive endpoints

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MongoDB

### Installation

1. Clone the repository
2. Install dependencies:

```bash
cd backend
npm install
```

3. Create a `.env` file based on `.env.example`:

```
# Server configuration
PORT=5000
NODE_ENV=development

# Database configuration
MONGODB_URI=mongodb://localhost:27017/taptrap

# Security settings - IMPORTANT: Use strong, unique values in production!
JWT_SECRET=generate-a-strong-random-string-at-least-32-chars
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=use-a-strong-password-with-mixed-case-numbers-symbols
ENCRYPTION_KEY=generate-a-different-strong-key-for-encryption

# CORS configuration
CORS_ORIGINS=https://yourdomain.com,http://localhost:3000

# Development settings
# BYPASS_AUTH=true  # Uncomment to bypass authentication in development
```

> **Note**: The `ENCRYPTION_KEY` is used to encrypt responses from sensitive endpoints. If not provided, responses will be sent unencrypted.

### Usage

#### Starting the server

Development mode:
```bash
npm run dev
```

Production mode:
```bash
npm start
```

#### Database Setup

Create the admin user:
```bash
npm run seed:admin
```

Import Airtable data (optional):
1. Export your Airtable data to JSON
2. Place the exported file at `backend/data/airtableExport.json`
3. Run the import script:
```bash
npm run import:airtable
```

## API Endpoints

### Public Endpoints

- `GET /api/content` - Get all game content (encrypted when ENCRYPTION_KEY is set)
- `GET /api/content/raw` - Get all raw content data (unformatted)
- `GET /api/content/:gameMode/:category` - Get content by game mode and category

### Admin Endpoints

- `POST /api/admin/login` - Admin login
- `GET /api/admin/content` - Get all content (admin view)
- `POST /api/admin/content` - Create new content
- `GET /api/admin/content/:id` - Get content by ID
- `PUT /api/admin/content/:id` - Update content
- `DELETE /api/admin/content/:id` - Delete content

## Admin Dashboard

Access the admin dashboard at `taptrap.app/admin`

## Deployment

The backend can be deployed to services like:

- Vercel
- Railway
- Render
- Heroku
- AWS

## Documentation

- [API Documentation](docs/API.md) - Detailed API documentation
- [MongoDB Implementation](docs/MONGODB_IMPLEMENTATION.md) - MongoDB setup and usage
- [Encryption](docs/ENCRYPTION.md) - Response encryption implementation