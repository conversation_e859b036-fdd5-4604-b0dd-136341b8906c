# AI Content Generation Feature

## Overview

The AI Content Generation feature allows administrators to automatically generate new questions and dares for the TapTrap game using OpenAI's GPT models. This feature integrates seamlessly with the existing admin panel and content management system.

## Features

- **Intelligent Content Generation**: Uses OpenAI's GPT-4o-mini model to generate contextually appropriate content
- **Category-Aware Prompts**: Tailored prompts for each game mode and difficulty category
- **Multi-Language Support**: Generates content in English, Spanish, and Dominican Spanish
- **Content Review System**: Review and select generated content before adding to database
- **Bulk Operations**: Add multiple selected items to the database at once
- **Context-Aware**: Uses existing content as reference to maintain consistency

## Setup

### 1. OpenAI API Key

1. Sign up for an OpenAI account at [https://platform.openai.com](https://platform.openai.com)
2. Generate an API key from your OpenAI dashboard
3. Add the API key to your backend environment variables:

```bash
# In backend/.env
OPENAI_API_KEY=your-openai-api-key-here
```

### 2. Install Dependencies

The OpenAI package is automatically installed when you run:

```bash
cd backend
npm install
```

## Usage

### Accessing the Feature

1. Log into the admin panel
2. Navigate to "Content Management"
3. Click the "Generate Content" button (lightning bolt icon)

### Generating Content

1. **Select Game Mode**: Choose between "Questions" or "Dares"
2. **Select Category**: Choose the difficulty level:
   - **Casual**: Light, fun, appropriate for all audiences
   - **Mild**: Slightly more personal but still comfortable
   - **Spicy**: More intimate and revealing content
   - **No Limits**: Very personal and bold content
3. **Generate**: Click "Generate Content" to create 8 new items
4. **Review**: Review the generated content in the table
5. **Select**: Choose which items to add to the database
6. **Add**: Click "Add Selected to Database" to save chosen items

### Content Review Interface

The generated content is displayed in a table with:
- **Selection checkboxes**: Choose individual items
- **Multi-language columns**: English, Spanish, Dominican Spanish
- **Bulk selection**: "Select All" / "Deselect All" options
- **Selection counter**: Shows how many items are selected

## Technical Implementation

### Backend Components

#### AI Content Service (`backend/src/services/aiContentService.js`)
- Handles OpenAI API integration
- Generates contextually appropriate prompts
- Validates and processes AI responses
- Manages bulk content creation

#### API Endpoints
- `POST /api/admin/content/generate` - Generate AI content
- `POST /api/admin/content/bulk-create` - Bulk create selected content

#### Content Generation Process
1. Fetches existing content for context (up to 10 examples)
2. Generates category-specific prompts
3. Calls OpenAI API with structured prompts
4. Validates and parses JSON responses
5. Returns formatted content array

### Frontend Components

#### Generate Content Page (`admin/src/pages/GenerateContent.js`)
- Main interface for AI content generation
- Form controls for game mode and category selection
- Content review table with selection capabilities
- Bulk operations and confirmation modals

#### AI Content Service (`admin/src/services/aiContent.service.js`)
- Frontend API communication
- Content validation utilities
- Category and game mode helpers

## Prompt Engineering

The system uses carefully crafted prompts that:
- Specify the exact category and difficulty level
- Provide context from existing content
- Request specific JSON format responses
- Include multi-language requirements
- Maintain brand voice and style consistency

### Example Prompt Structure

```
You are a creative content generator for TapTrap, a party game app. 
Generate 8 new questions for the "Casual Questions - Light, fun, and appropriate for all audiences" category.

Requirements:
- Content must be appropriate for the category level
- Each item should be unique and engaging
- Provide content in 3 languages: English, Spanish, and Dominican Spanish
- Keep content concise but interesting
- Avoid repetition with existing content

[Existing content examples for context...]

Please generate 8 new questions in the following JSON format:
[
  {
    "text_en": "English version",
    "text_es": "Spanish version", 
    "text_dom": "Dominican Spanish version"
  }
]
```

## Error Handling

The system includes comprehensive error handling for:
- **Missing API Key**: Clear error messages when OpenAI key is not configured
- **API Quota Exceeded**: Specific handling for OpenAI rate limits
- **Invalid Responses**: JSON parsing and validation errors
- **Network Issues**: Connection and timeout handling
- **Content Validation**: Ensures all required fields are present

## Security Considerations

- **API Key Protection**: OpenAI API key is stored securely in environment variables
- **Authentication**: All AI endpoints require admin authentication
- **Input Validation**: Strict validation of game modes and categories
- **Content Review**: All generated content must be manually reviewed before publication

## Cost Management

- Uses GPT-4o-mini model for cost efficiency
- Generates 8 items per request to balance cost and utility
- Includes context examples to improve quality and reduce regeneration needs
- Provides clear feedback on generation success/failure

## Troubleshooting

### Common Issues

1. **"AI service is not available"**
   - Check that OPENAI_API_KEY is set in environment variables
   - Verify the API key is valid and has sufficient credits

2. **"Failed to parse AI response"**
   - The AI occasionally returns malformed JSON
   - Simply try generating again

3. **"No valid content items were generated"**
   - The AI response didn't include required fields
   - Try generating again or check API key permissions

### Monitoring

- All AI operations are logged in the backend console
- Generation success/failure rates can be monitored through logs
- API usage can be tracked through OpenAI dashboard

## Future Enhancements

Potential improvements for future versions:
- **Custom Prompts**: Allow admins to customize generation prompts
- **Batch Generation**: Generate content for multiple categories at once
- **Content Templates**: Save and reuse successful prompt templates
- **Quality Scoring**: Rate generated content quality automatically
- **Usage Analytics**: Track generation patterns and success rates
