# MongoDB Implementation Guide for TapTrap

Este documento proporciona una guía completa de la implementación de MongoDB en el proyecto TapTrap, incluyendo la configuración de la base de datos, los modelos de datos, los servicios y las herramientas de prueba.

## Tabla de Contenidos

1. [Configuración de MongoDB](#configuración-de-mongodb)
2. [Estructura de la Base de Datos](#estructura-de-la-base-de-datos)
3. [Modelos de Datos](#modelos-de-datos)
4. [Servicios](#servicios)
5. [DTOs (Data Transfer Objects)](#dtos-data-transfer-objects)
6. [Scripts de Prueba](#scripts-de-prueba)
7. [Guía de Uso](#guía-de-uso)

## Configuración de MongoDB

### Cadena de Conexión

La aplicación utiliza MongoDB Atlas como base de datos principal. La cadena de conexión se configura a través de la variable de entorno `MONGODB_URI` en el archivo `.env`:

```
MONGODB_URI=your_mongodb_connection_string
```

**IMPORTANTE**: Nunca incluyas credenciales reales en archivos de documentación o código fuente. Utiliza siempre variables de entorno para almacenar información sensible.

### Opciones de Conexión

La conexión a MongoDB se configura con las siguientes opciones para optimizar el rendimiento:

```javascript
const defaultMongooseOptions = {
  serverSelectionTimeoutMS: 15000, // Timeout para selección de servidor
  socketTimeoutMS: 30000,         // Timeout para operaciones de socket
  connectTimeoutMS: 30000,        // Timeout para conexión inicial
  dbName: 'test'                  // Nombre de la base de datos
};
```

### Servicio de Base de Datos

El servicio de base de datos (`DatabaseService`) proporciona métodos para conectar y desconectar de MongoDB, así como para obtener información sobre el estado de la conexión:

```javascript
// Conectar a MongoDB
await databaseService.connect();

// Obtener estado de la conexión
const connectionStatus = databaseService.getConnectionStatus();

// Desconectar de MongoDB
await databaseService.disconnect();
```

## Estructura de la Base de Datos

La base de datos "test" contiene las siguientes colecciones:

### Colección "test.content"

Almacena el contenido del juego (preguntas y retos):

```javascript
{
  _id: ObjectId,              // ID generado por MongoDB
  id: String,                 // ID único generado por la aplicación
  text_en: String,            // Texto en inglés
  text_es: String,            // Texto en español
  category: String,           // Categoría (casual_questions, mild_questions, etc.)
  gameMode: String,           // Modo de juego (questions, dares)
  active: Boolean,            // Estado activo
  createdAt: Date,            // Fecha de creación
  updatedAt: Date,            // Fecha de actualización
  __v: Number                 // Versión (generado por Mongoose)
}
```

### Colección "users"

Almacena información de usuarios administradores:

```javascript
{
  _id: ObjectId,              // ID generado por MongoDB
  email: String,              // Correo electrónico (único)
  password: String,           // Contraseña (hash)
  role: String,               // Rol (admin)
  createdAt: Date,            // Fecha de creación
  updatedAt: Date,            // Fecha de actualización
  __v: Number                 // Versión (generado por Mongoose)
}
```

## Modelos de Datos

### Modelo Base

El modelo base (`BaseEntity`) proporciona funcionalidad común para todos los modelos:

```javascript
const createBaseSchema = (definition, options = {}) => {
  // Agregar campos comunes a todos los esquemas
  const baseDefinition = {
    ...definition,
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  };

  // Crear esquema con opciones proporcionadas
  const schema = new mongoose.Schema(baseDefinition, options);

  // Agregar hook pre-save para actualizar el campo updatedAt
  schema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
  });

  return schema;
};
```

### Modelo Content

El modelo Content define la estructura del contenido del juego:

```javascript
// Definición del esquema de contenido
const contentSchemaDefinition = {
  id: {
    type: String,
    required: true,
    unique: true
  },
  text_en: {
    type: String,
    required: true
  },
  text_es: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: [
      'casual_questions', 'mild_questions', 'spicy_questions', 'no_limits_questions',
      'casual_dares', 'mild_dares', 'spicy_dares', 'no_limits_dares'
    ]
  },
  gameMode: {
    type: String,
    required: true,
    enum: ['questions', 'dares']
  },
  active: {
    type: Boolean,
    default: true
  }
};

// Crear el esquema usando el creador de esquema base
const contentSchema = createBaseSchema(contentSchemaDefinition);

// Especificar explícitamente el nombre de la colección como 'test.content'
const Content = mongoose.model('Content', contentSchema, 'test.content');
```

### Modelo User

El modelo User define la estructura de los usuarios administradores:

```javascript
// Definición del esquema de usuario
const userSchemaDefinition = {
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['admin'],
    default: 'admin'
  }
};

// Crear el esquema usando el creador de esquema base
const userSchema = createBaseSchema(userSchemaDefinition);

// Agregar método para comparar contraseñas
userSchema.methods.comparePassword = async function(candidatePassword) {
  // Comparación de contraseñas
  const isMatch = await bcrypt.compare(candidatePassword, this.password);
  return isMatch;
};

// Especificar explícitamente el nombre de la colección como 'users'
const User = mongoose.model('User', userSchema, 'users');
```

## Servicios

### Servicio de Contenido

El servicio de contenido (`ContentService`) proporciona métodos para gestionar el contenido del juego:

```javascript
// Obtener todo el contenido
const allContent = await contentService.getAllContent();

// Obtener contenido por ID
const content = await contentService.getContentById(id);

// Crear nuevo contenido
const newContent = await contentService.createContent(contentData);

// Actualizar contenido
const updatedContent = await contentService.updateContent(id, contentData);

// Eliminar contenido
const result = await contentService.deleteContent(id);

// Obtener contenido formateado para la app
const formattedContent = await contentService.getFormattedContent();
```

### Servicio de Usuario

El servicio de usuario (`UserService`) proporciona métodos para gestionar usuarios:

```javascript
// Crear nuevo usuario
const newUser = await userService.createUser(userData);

// Autenticar usuario
const authResult = await userService.authenticateUser(email, password);

// Obtener usuario por ID
const user = await userService.getUserById(id);

// Actualizar usuario
const updatedUser = await userService.updateUser(id, userData);
```

## DTOs (Data Transfer Objects)

### DTO Base

El DTO base (`BaseDTO`) proporciona métodos comunes para validar y transformar datos:

```javascript
class BaseDTO {
  // Validar campos requeridos
  static validateRequired(data, requiredFields) {
    // Validación de campos requeridos
  }

  // Transformar datos de API a formato de entidad
  static toEntity(data) {
    // Transformación a entidad
  }

  // Transformar entidad a formato de API
  static fromEntity(entity) {
    // Transformación desde entidad
  }
}
```

### DTO de Contenido

El DTO de contenido (`ContentDTO`) valida y transforma datos de contenido:

```javascript
class ContentDTO extends BaseDTO {
  // Validar datos de contenido
  static validate(data) {
    // Validación de datos
  }

  // Transformar datos de contenido de API a formato de entidad
  static toEntity(data) {
    // Transformación a entidad
  }

  // Transformar entidad de contenido a formato de API
  static fromEntity(entity) {
    // Transformación desde entidad
  }
}
```

### DTO de Usuario

El DTO de usuario (`UserDTO`) valida y transforma datos de usuario:

```javascript
class UserDTO extends BaseDTO {
  // Validar datos de usuario
  static validate(data) {
    // Validación de datos
  }

  // Transformar datos de usuario de API a formato de entidad
  static toEntity(data) {
    // Transformación a entidad
  }

  // Transformar entidad de usuario a formato de API (sin datos sensibles)
  static fromEntity(entity) {
    // Transformación desde entidad
  }
}
```

## Scripts de Prueba

Se han creado varios scripts para probar la conexión a MongoDB y las operaciones con las colecciones:

### Scripts para Contenido

- `test-fetch-content.js`: Obtiene todo el contenido de la colección "test.content"
- `test-search-content.js`: Busca contenido específico por criterios
- `test-add-content.js`: Agrega un nuevo elemento de contenido
- `test-content-service.js`: Prueba todas las operaciones del servicio de contenido

### Scripts para Usuarios

- `test-users.js`: Obtiene todos los usuarios de la colección "users"
- `test-create-admin.js`: Crea un usuario administrador si no existe
- `test-auth.js`: Prueba la autenticación de usuarios

### Scripts Generales

- `test-mongodb-connection.js`: Prueba la conexión a MongoDB Atlas
- `copy-env.js`: Copia el archivo .env al directorio src

## Guía de Uso

### Configuración Inicial

1. Crear un archivo `.env` en el directorio `backend` con la siguiente configuración:

```
# Server configuration
PORT=5000
NODE_ENV=development

# Database configuration
MONGODB_URI=your_mongodb_connection_string

# Security settings - IMPORTANT: Use strong, unique values in production!
JWT_SECRET=generate_a_strong_random_string_at_least_32_chars
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=use_a_strong_password_with_mixed_case_numbers_symbols
ENCRYPTION_KEY=generate_a_different_strong_key_for_encryption

# CORS configuration
CORS_ORIGINS=https://yourdomain.com,http://localhost:3000
```

**IMPORTANTE**: Utiliza valores seguros y únicos para las claves secretas y contraseñas, especialmente en entornos de producción.

2. Copiar el archivo `.env` al directorio `src` (opcional):

```bash
npm run copy-env
```

### Pruebas de Conexión

Para probar la conexión a MongoDB:

```bash
npm run test:mongodb
```

### Operaciones con Contenido

Para obtener todo el contenido:

```bash
npm run test:fetch
```

Para buscar contenido específico:

```bash
node src/test-search-content.js questions casual_questions
```

Para agregar un nuevo elemento de contenido:

```bash
npm run test:add
```

Para probar todas las operaciones del servicio de contenido:

```bash
npm run test:service
```

### Operaciones con Usuarios

Para ver todos los usuarios:

```bash
npm run test:users
```

Para crear un usuario administrador:

```bash
npm run create:admin
```

Para probar la autenticación:

```bash
npm run test:auth
```

Para probar la autenticación con credenciales específicas:

```bash
node src/test-auth.js <EMAIL> your_password
```

### Iniciar la Aplicación

Para iniciar la aplicación en modo desarrollo:

```bash
npm run dev
```

Para iniciar la aplicación en modo producción:

```bash
npm start
```
