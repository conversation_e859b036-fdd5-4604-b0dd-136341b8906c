# TapTrap Backend Integration

This document explains how the TapTrap mobile app integrates with the new custom backend, replacing Airtable.

## Overview

The TapTrap backend consists of:

1. **RESTful API**: Node.js/Express server with MongoDB
2. **Admin Dashboard**: React-based interface at taptrap.app/admin

## API Endpoints

### Public Endpoints

- `GET /api/content` - Returns all game content formatted for the app (encrypted when ENCRYPTION_KEY is set)

### Admin Endpoints (Protected)

- `POST /api/admin/login` - Admin authentication
- `GET /api/admin/content` - Get all content
- `POST /api/admin/content` - Create new content
- `GET /api/admin/content/:id` - Get content by ID
- `PUT /api/admin/content/:id` - Update content
- `DELETE /api/admin/content/:id` - Delete content

### Penalty Endpoints

- `GET /api/penalties` - Returns all penalty content formatted for the app (encrypted when ENCRYPTION_KEY is set)
- `GET /api/admin/penalties` - Get all penalties (admin)
- `POST /api/admin/penalties` - Create new penalty (admin)
- `GET /api/admin/penalties/:id` - Get penalty by ID (admin)
- `PUT /api/admin/penalties/:id` - Update penalty (admin)
- `DELETE /api/admin/penalties/:id` - Delete penalty (admin)

## Data Structure

Content in MongoDB follows this schema:

```
{
  id: String,            // Unique identifier
  text_en: String,       // English text
  text_es: String,       // Spanish text
  category: String,      // e.g., "casual_questions", "mild_dares", etc.
  gameMode: String,      // "questions" or "dares"
  active: Boolean,       // Whether to include in app content
  createdAt: Date,       // Creation timestamp
  updatedAt: Date        // Last update timestamp
}
```

The API formats this data into the structure expected by the app:

```
{
  questions: {
    casual_questions: [{ id, text_en, text_es }],
    mild_questions: [...],
    spicy_questions: [...],
    no_limits_questions: [...]
  },
  dares: {
    casual_dares: [...],
    mild_dares: [...],
    spicy_dares: [...],
    no_limits_dares: [...]
  }
}
```

### Penalty Data Structure

Penalties in MongoDB follow this schema:

```
{
  id: String,              // Unique identifier
  text_en: String,         // English text
  text_es: String,         // Spanish text
  text_dom: String,        // Dominican Spanish text
  category: String,        // e.g., "drinking", "physical", "social", "silly", "creative"
  active: Boolean,         // Whether to include in app content
  isPremium: Boolean,      // Whether penalty is premium-only (default: false)
  isDefaultFree: Boolean,  // Whether penalty is default for free users (default: false)
  isDefaultPremium: Boolean, // Whether penalty is default for premium users (default: false)
  createdAt: Date,         // Creation timestamp
  updatedAt: Date          // Last update timestamp
}
```

The API formats penalty data into this structure for the app:

```
{
  drinking: [{ id, text_en, text_es, text_dom, isPremium, isDefaultFree, isDefaultPremium }],
  physical: [...],
  social: [...],
  silly: [...],
  creative: [...]
}
```

### Premium Penalty System

The backend supports a comprehensive premium penalty system:

- **Premium Penalties**: Only accessible to users with premium subscriptions
- **Default Penalties**: Automatically selected based on user's premium status
- **Mutual Exclusivity**: Premium penalties cannot be default for free users
- **Single Default**: Only one penalty can be marked as default per user type
- **Backward Compatibility**: Existing penalties work with new fields (defaults to false)

## Mobile App Integration

The mobile app connects to the backend using the `contentService.ts` file, which:

1. Tries to fetch data from the backend API when the app starts
2. Falls back to cached content if the backend is unreachable
3. Maintains the same data structure as before, so no changes were needed to the app's components

## Admin Dashboard

The admin dashboard provides a user-friendly interface for managing game content:

1. **Login Page**: Secure admin access
2. **Dashboard**: Overview of content statistics
3. **Content List**: Browse, filter, and search all content
4. **Add/Edit Forms**: Create and modify content items

## Local Development

To run the backend locally:

1. **Setup environment**

   ```bash
   cd backend
   cp .env.example .env
   # Edit .env with your MongoDB connection and admin credentials
   ```

2. **Start backend API**

   ```bash
   cd backend
   npm install
   npm run dev
   ```

3. **Start admin dashboard**

   ```bash
   cd backend/admin
   npm install
   npm start
   ```

4. **Test with mobile app**

   Edit `services/contentService.ts` to point to your local API:

   ```typescript
   const API_URL = 'http://localhost:5000/api';
   ```

## Database Migrations

The backend includes a migration system for schema updates:

### Running Penalty Premium Fields Migration

After deploying the premium penalty system, run the migration to add premium fields to existing penalties:

```bash
cd backend
npm run migrate:penalties
```

Or run directly:

```bash
cd backend
node scripts/migrate-penalties.js
```

The migration will:
- Add `isPremium`, `isDefaultFree`, and `isDefaultPremium` fields to existing penalties
- Set default values (`false` for all fields)
- Verify successful completion
- Provide detailed logging

See `backend/migrations/README.md` for detailed migration documentation.

## Deployment

See the `backend/DEPLOYMENT.md` file for detailed deployment instructions.

## Migrating from Airtable

To import content from your existing Airtable:

1. Export Airtable data to JSON format
2. Place in `backend/data/airtableExport.json`
3. Run `npm run import:airtable` from the backend directory

## Security

The backend implements several security measures:

- JWT authentication for admin access
- Password hashing using bcrypt
- Response encryption for sensitive endpoints
- Environment variables for sensitive information
- API rate limiting (in production)

## Troubleshooting

If the app can't connect to the backend:

1. Check that the API_URL in `contentService.ts` is correct
2. Verify the backend is running and accessible
3. Check for any CORS issues
4. The app will still work with cached content even if the backend is unavailable