# Vercel Deployment Instructions for TapTrap Backend

This document outlines the steps to deploy the TapTrap backend to Vercel.

## Prerequisites

1. A Vercel account (create one at https://vercel.com)
2. Vercel CLI installed: `npm install -g vercel`
3. MongoDB Atlas account with a cluster set up

## Deployment Steps

### 1. Install Vercel CLI and Login

```bash
npm install -g vercel
vercel login
```

### 2. Deploy to Vercel

Navigate to the backend directory and run:

```bash
cd /path/to/TapTrap/backend
vercel
```

Follow the prompts:
- Select your scope (personal or team)
- Set up your project settings (Vercel will detect it's a Node.js project)
- Confirm deployment

### 3. Set Environment Variables in Vercel Dashboard

After initial deployment, go to the Vercel dashboard and add these environment variables:

- `MONGODB_URI`: Your MongoDB Atlas connection string
- `JWT_SECRET`: Secret key for JWT token generation
- `ADMIN_EMAIL`: Email for admin login
- `ADMIN_PASSWORD`: Password for admin login
- `NODE_ENV`: Set to "production"
- `CORS_ORIGINS`: Comma-separated list of allowed origins (e.g., "https://taptrap.app,https://admin.taptrap.app")

You can set these through the Vercel web dashboard:
1. Go to your project
2. Click on "Settings"
3. Select "Environment Variables"
4. Add each variable with its value

### 4. Redeploy with Environment Variables

```bash
vercel --prod
```

### 5. Set Up Custom Domain (Optional)

In the Vercel dashboard:
1. Go to your project settings
2. Click on "Domains"
3. Add your custom domain (e.g., taptrap.app or api.taptrap.app)
4. Follow Vercel's instructions to configure DNS settings

### 6. Update Mobile App Connection

Update your mobile app's API URL to point to your new Vercel deployment:

```typescript
// In services/contentService.ts
const API_URL = 'https://your-vercel-deployment-url.vercel.app/api';
// or if using custom domain
const API_URL = 'https://api.taptrap.app/api';
```

## Testing Your Deployment

1. Check the health endpoint:
   ```
   curl https://your-vercel-deployment-url.vercel.app/api/health
   ```
   Should return: `{"status":"ok"}`

2. Access the admin panel:
   ```
   https://your-vercel-deployment-url.vercel.app/admin
   ```
   Login with your admin credentials

## Troubleshooting

1. **MongoDB Connection Issues**:
   - Ensure your MongoDB Atlas IP whitelist includes Vercel's IPs or is set to allow access from anywhere
   - Verify your connection string is correct in environment variables

2. **Admin Panel Not Loading**:
   - Check the Vercel build logs for the admin panel build
   - Ensure REACT_APP_API_URL is set correctly

3. **API Errors**:
   - Check Vercel function logs for detailed error messages
   - Verify all required environment variables are set

## Maintenance

To deploy updates:

```bash
cd /path/to/TapTrap/backend
vercel --prod
```

Remember to update your environment variables if they change.