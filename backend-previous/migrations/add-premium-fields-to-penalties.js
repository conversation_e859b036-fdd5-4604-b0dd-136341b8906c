/**
 * Migration: Add premium fields to existing penalties
 * 
 * This migration adds the new premium-related fields to existing penalty documents:
 * - isPremium: false (default)
 * - isDefaultFree: false (default)
 * - isDefaultPremium: false (default)
 * 
 * Run this migration after deploying the new penalty model to ensure
 * all existing penalties have the new fields.
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Migration function
const migratePenalties = async () => {
  try {
    console.log('🔄 Starting penalty migration...');

    // Get the penalties collection directly
    const db = mongoose.connection.db;
    const penaltiesCollection = db.collection('penalties');

    // Count existing penalties
    const totalPenalties = await penaltiesCollection.countDocuments();
    console.log(`📊 Found ${totalPenalties} penalties to migrate`);

    if (totalPenalties === 0) {
      console.log('ℹ️  No penalties found to migrate');
      return;
    }

    // Update all penalties that don't have the new fields
    const result = await penaltiesCollection.updateMany(
      {
        $or: [
          { isPremium: { $exists: false } },
          { isDefaultFree: { $exists: false } },
          { isDefaultPremium: { $exists: false } }
        ]
      },
      {
        $set: {
          isPremium: false,
          isDefaultFree: false,
          isDefaultPremium: false,
          updatedAt: new Date()
        }
      }
    );

    console.log(`✅ Migration completed successfully!`);
    console.log(`📈 Updated ${result.modifiedCount} penalties`);
    console.log(`📋 Matched ${result.matchedCount} penalties`);

    // Verify the migration
    const penaltiesWithNewFields = await penaltiesCollection.countDocuments({
      isPremium: { $exists: true },
      isDefaultFree: { $exists: true },
      isDefaultPremium: { $exists: true }
    });

    console.log(`✅ Verification: ${penaltiesWithNewFields}/${totalPenalties} penalties now have premium fields`);

    if (penaltiesWithNewFields === totalPenalties) {
      console.log('🎉 All penalties successfully migrated!');
    } else {
      console.log('⚠️  Some penalties may not have been migrated. Please check manually.');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
};

// Main execution
const runMigration = async () => {
  try {
    await connectDB();
    await migratePenalties();
    console.log('🏁 Migration process completed');
  } catch (error) {
    console.error('💥 Migration process failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
};

// Run the migration if this file is executed directly
if (require.main === module) {
  console.log('🚀 Starting penalty premium fields migration...');
  runMigration();
}

module.exports = {
  migratePenalties,
  runMigration
};
