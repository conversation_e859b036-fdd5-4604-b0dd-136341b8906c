#!/usr/bin/env node

/**
 * Penalty Migration Runner
 * 
 * This script runs the penalty premium fields migration.
 * 
 * Usage:
 *   node scripts/migrate-penalties.js
 * 
 * Make sure to set the MONGODB_URI environment variable before running.
 */

const path = require('path');
const { runMigration } = require('../migrations/add-premium-fields-to-penalties');

console.log('🎯 TapTrap Penalty Migration Runner');
console.log('=====================================');
console.log('');

// Check if MONGODB_URI is set
if (!process.env.MONGODB_URI) {
  console.error('❌ Error: MONGODB_URI environment variable is not set');
  console.log('');
  console.log('Please set the MONGODB_URI environment variable:');
  console.log('  export MONGODB_URI="your-mongodb-connection-string"');
  console.log('');
  console.log('Or create a .env file in the backend directory with:');
  console.log('  MONGODB_URI=your-mongodb-connection-string');
  console.log('');
  process.exit(1);
}

console.log('🔗 MongoDB URI:', process.env.MONGODB_URI.replace(/\/\/.*@/, '//***:***@'));
console.log('');

// Run the migration
runMigration().catch((error) => {
  console.error('💥 Migration failed:', error);
  process.exit(1);
});
