# MongoDB Setup for TapTrap

This guide explains how to set up and configure MongoDB for your TapTrap application.

## MongoDB Atlas Setup

TapTrap uses MongoDB Atlas for production deployments. Here's how to set it up:

### 1. Create a MongoDB Atlas Account

1. Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas/register)
2. Create a free account or sign in to your existing account

### 2. Create a Cluster

1. Create a new project (if needed)
2. Click "Build a Database"
3. Choose the free tier (M0)
4. Select your preferred cloud provider and region (ideally close to your Vercel region)
5. Name your cluster (e.g., "taptrap-db")
6. Click "Create"

### 3. Configure Database Access

1. In the left sidebar, click "Database Access"
2. Click "Add New Database User"
3. Create a username and a strong password
   - Choose "Password" authentication method
   - Save these credentials as you'll need them for your connection string
4. Set database user privileges to "Read and Write to Any Database"
5. Click "Add User"

### 4. Configure Network Access

For development:
1. In the left sidebar, click "Network Access"
2. Click "Add IP Address"
3. Click "Allow Access from Anywhere" (for development only)
4. Click "Confirm"

For production:
1. Add the Vercel IP ranges (check Vercel documentation for current IPs)
2. You can also use "Allow Access from Anywhere" for simplicity, but it's less secure

### 5. Get Connection String

1. Go to your cluster and click "Connect"
2. Choose "Connect your application"
3. Select "Node.js" and the appropriate version
4. Copy the connection string - it will look like:
   ```
   mongodb+srv://<username>:<password>@taptrap-db.mongodb.net/taptrap?retryWrites=true&w=majority
   ```
5. Replace `<username>` and `<password>` with your database user credentials

### 6. Configure Vercel Environment Variables

1. In your Vercel project settings, add the following environment variable:
   - Key: `MONGODB_URI`
   - Value: Your MongoDB connection string (with username and password)

2. Also add a JWT_SECRET environment variable:
   - Key: `JWT_SECRET`
   - Value: A secure random string (use a password generator to create one)

## Connection Troubleshooting

If you encounter connection issues like timeouts when deploying to Vercel:

### Check Your Connection String

- Ensure the username and password in the connection string are URL encoded (special characters escaped)
- Verify the cluster name in the connection string is correct

### Network Access

- Verify that network access is properly configured to allow connections from Vercel

### MongoDB Atlas Status

- Check the [MongoDB Atlas Status Page](https://status.mongodb.com/) for any ongoing issues

### Connection Performance

The application uses the following MongoDB connection optimizations:

```javascript
const mongooseOptions = {
  serverSelectionTimeoutMS: 15000, // Timeout for server selection
  socketTimeoutMS: 30000,         // Timeout for socket operations
  connectTimeoutMS: 30000,        // Timeout for initial connection
  maxPoolSize: 5,                 // Reduce pool size for serverless
  minPoolSize: 1,                 // Keep at least one connection
  maxIdleTimeMS: 10000,           // Close idle connections quicker
};
```

These settings are optimized for serverless environments and should help prevent timeouts.

### Vercel Logs

If you continue to encounter issues:
1. Go to your Vercel deployment
2. Check the Function Logs for error messages
3. Look for messages containing "MongoDB" or "mongoose" to identify connection problems