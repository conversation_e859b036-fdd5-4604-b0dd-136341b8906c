/**
 * <PERSON>ript to copy .env file to src directory
 * 
 * This script copies the .env file from the backend directory to the src directory
 * Run with: node copy-env.js
 */

const fs = require('fs');
const path = require('path');

// Paths
const sourceEnvPath = path.resolve(__dirname, '.env');
const targetEnvPath = path.resolve(__dirname, 'src', '.env');

// Check if source .env exists
if (!fs.existsSync(sourceEnvPath)) {
  console.error('Error: .env file not found in backend directory!');
  console.error('Expected path:', sourceEnvPath);
  process.exit(1);
}

// Create src directory if it doesn't exist
const srcDir = path.resolve(__dirname, 'src');
if (!fs.existsSync(srcDir)) {
  console.error('Error: src directory not found!');
  console.error('Expected path:', srcDir);
  process.exit(1);
}

// Copy the file
try {
  fs.copyFileSync(sourceEnvPath, targetEnvPath);
  console.log('✅ .env file copied successfully to src directory!');
  console.log('Source:', sourceEnvPath);
  console.log('Target:', targetEnvPath);
} catch (error) {
  console.error('Error copying .env file:', error.message);
  process.exit(1);
}
