# Vercel Deployment Checklist

We've made several optimizations to fix the Vercel deployment issues. Here's a checklist to ensure a successful deployment:

## 1. Pre-Deployment Checks

- [x] Backend API working correctly with MongoDB Atlas
- [x] Admin panel builds successfully locally
- [x] vercel.json is configured correctly
- [x] All environment variables are set up

## 2. Vercel Configuration

We've optimized the vercel.json file:
- Updated the build configuration to point directly to admin/package.json
- Set the correct distDir path ("build" since it's relative to admin directory)
- Improved route configuration to properly handle API and static assets
- Added leading slashes to the destination paths
- Added specific static asset routes to prevent API conflicts

## 3. Environment Variables

Make sure these environment variables are set in your Vercel project:

- MONGODB_URI=your_mongodb_connection_string
- JWT_SECRET=generate_a_strong_random_string_at_least_32_chars
- ADMIN_EMAIL=<EMAIL>
- ADMIN_PASSWORD=use_a_strong_password_with_mixed_case_numbers_symbols
- NODE_ENV=production  <-- This is important!
- CORS_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com,http://localhost:3000
- ENCRYPTION_KEY=generate_a_different_strong_key_for_encryption

**IMPORTANT**: Never commit real credentials to your repository. Always use environment variables in Vercel's dashboard.

## 4. Deployment Steps

1. Commit all changes:
```bash
git add .
git commit -m "Optimize Vercel deployment configuration"
```

2. Deploy to Vercel:
```bash
vercel
```

3. Check the deployed preview URL first:
   - Verify the API works at /api/health
   - Check the admin panel at /admin
   - Test the admin login

4. If everything works, deploy to production:
```bash
vercel --prod
```

## 5. Troubleshooting

If deployment still fails:

1. **Check the deployment logs**:
```bash
vercel logs <deployment-url>
```

2. **Try deploying again with debugging**:
```bash
vercel --debug
```

3. **Verify that you selected the correct project directory**:
   Make sure you're in the backend directory when deploying.

4. **Check the build output**:
   In the Vercel dashboard, look at the build logs for errors.

5. **Try clearing the cache**:
```bash
vercel --force
```

## 6. Verification After Deployment

Once deployed, verify these endpoints:

- API health check: https://<your-domain>/api/health
- Admin login page: https://<your-domain>/admin
- API content endpoint: https://<your-domain>/api/content

## 7. Next Steps After Successful Deployment

1. Update the mobile app's API URL to point to your new backend
2. Create a custom domain in Vercel if needed
3. Update DNS settings for your domain

If you're still experiencing issues, consider reaching out to Vercel support with your deployment logs.